---
alwaysApply: true
---

## Reference Table: Rule Files by Execution Phase

| Phase                                 | Rule File(s)                                      |
|---------------------------------------|---------------------------------------------------|
| **All Phases (Tool Usage, Reasoning)**| @.cursor/rules/mcp-servers-usage-constitution.mdc               |
| Requirement Clarification             | @.cursor/rules/constitution-entrypoint.mdc (this file)          |
| Context & Knowledge Retrieval         | @.cursor/rules/byterover-rules.mdc                              |
| Folder Structure & Placement          | @.cursor/rules/folder-structure-constitution.mdc                |
| Code Standards & Best Practices       | @.cursor/rules/fastapi-code-standard-constitution.mdc           |
| Feature Development Reference         | @.cursor/rules/fastapi-feature-development-reference.mdc        |
| Planning, Reasoning, Stepwise Logic   | @.cursor/rules/mcp-servers-usage-constitution.mdc               |
| Progress Tracking & Memory            | @.cursor/rules/mcp-servers-usage-constitution.mdc, @.cursor/rules/byterover-rules.mdc |
| Review & Audit                        | All of the above                                  |

---

## 1. Universal Principles

- **Always apply all rules in this file and referenced constitutions in `.cursor/rules/`.**
- **No code, structure, or process is valid unless it complies with these rules.**
- **All deviations must be justified and documented as amendments.**
- **Regular audits are mandatory.**

---

## 2. Step-by-Step Strategic Execution Plan (Agent-Only)

### 2.1. Requirement Clarification (Pre-Planning)
- **If the requirement is unclear, ambiguous, or critical, ask the user for clarification before planning.**
- **Do not ask for confirmation or approval unless absolutely necessary (uncertainty, critical risk, or missing information).**
- **Once requirements are clear, proceed autonomously.**
- **Reference:** This file (@constitution-entrypoint.mdc)

### 2.2. Context & Knowledge Retrieval
- **Always use ByteRover MCP to retrieve all relevant prior knowledge, solutions, and best practices.**
- **Recall project-specific context, past decisions, and related discussions using agent memory.**
- **Reference:** @byterover-rules.mdc

### 2.3. Tool Usage, Planning & Reasoning
- **Before any planning, reasoning, or tool-driven step, consult @mcp-servers-usage-constitution.mdc for available MCP tools, their best practices, and reasoning strategies.**
- **Use Sequential Thinking and other MCP tools for stepwise planning, chunking, and dynamic reasoning as described in @mcp-servers-usage-constitution.mdc.**
- **Reference:** @mcp-servers-usage-constitution.mdc (must be checked first in every phase)

### 2.4. Folder Structure & Codebase Analysis
- **Use MCP tools (as described in @mcp-servers-usage-constitution.mdc) to analyze the codebase and folder structure.**
- **Cross-check the @/src folder structure and any new or existing code against @folder-structure-constitution.mdc.**
- **Determine correct placement for all new files/folders before coding.**
- **Reference:** @folder-structure-constitution.mdc

### 2.5. Code Standards & Best Practices
- **All code, API, and logic must comply with @fastapi-code-standard-constitution.mdc and @fastapi-feature-development-reference.mdc.**
- **No business logic in presentational or API route files. Centralize parsing, transformation, and business logic.**
- **Reference:** @fastapi-code-standard-constitution.mdc, @fastapi-feature-development-reference.mdc

### 2.6. Autonomous Plan Generation
- **Generate a detailed, step-by-step plan for the task, including:**
  - Context and knowledge retrieval steps.
  - Folder/file structure changes/additions.
  - Code, API, and logic changes.
  - Tool usage at each step (which MCP tool, for what purpose).
  - Testing, documentation, and review steps.
- **Each plan step must specify the tool or feature used and its purpose, referencing @mcp-servers-usage-constitution.mdc for tool selection.**
- **Break the plan into logical, manageable chunks/tasks.**
- **Reference:** @mcp-servers-usage-constitution.mdc

### 2.7. Autonomous Execution (Chunked, Stepwise)
- **Execute each chunk/task in order, using the specified tools and following all code standards and folder structure rules.**
- **After each chunk/task, cross-verify that everything was implemented or executed correctly:**
  - Use codebase search, grep, filesystem, or other tools as described in @mcp-servers-usage-constitution.mdc to check for correctness and compliance.
  - If an error or deviation is found, correct it before proceeding.
- **Reference:** @mcp-servers-usage-constitution.mdc, @fastapi-code-standard-constitution.mdc, @folder-structure-constitution.mdc

### 2.8. Progress Tracking & Resumption
- **Maintain a clear, up-to-date record of completed and pending tasks/chunks using the agent's to-do list and memory features.**
- **If interrupted, always resume from the exact stopping point, using the tracked progress.**
- **On 'continue' command, pick up precisely where execution stopped, with full clarity on next steps.**
- **Reference:** @mcp-servers-usage-constitution.mdc, @byterover-rules.mdc

### 2.9. Review & Audit
- **After all tasks are complete, cross-verify all changes against this constitution and referenced rules.**
- **Run all linting, formatting, and compliance checks.**
- **Perform a final audit using codebase search, filesystem, and memory tools as described in @mcp-servers-usage-constitution.mdc.**
- **Reference:** All rule files above

### 2.10. Knowledge Storage
- **After successful completion, store all critical solutions, insights, and best practices in ByteRover.**
- **Update agent memory with new context or decisions.**
- **Reference:** @byterover-rules.mdc

---

## 3. Rule File Usage Guide (Agent)

- **@mcp-servers-usage-constitution.mdc:** For all MCP tool usage, planning, reasoning, stepwise execution, verification, and audits. Always check this file first in every phase.
- **@byterover-rules.mdc:** For context retrieval and knowledge storage.
- **@folder-structure-constitution.mdc:** For folder and file structure compliance.
- **@fastapi-code-standard-constitution.mdc:** For backend code standards, modularity, and best practices.
- **@fastapi-feature-development-reference.mdc:** For feature development, tool selection, and integration best practices.
- **@constitution-entrypoint.mdc (this file):** For the step-by-step strategic execution plan and universal principles.

---

## 4. Folder & File Structure Enforcement
- **Always follow @folder-structure-constitution.mdc.**
- **No code, assets, or logic outside designated folders.**
- **Refactor any non-compliant code as part of the task.**

---

## 5. Code Standards & Best Practices
- **Strict Python typing, modularity, and code style as per @fastapi-code-standard-constitution.mdc.**
- **No business logic in API route or presentational files.**
- **Centralize parsing, transformation, and business logic.**
- **Use dependency injection, routers, and services as specified.**
- **Document all exported functions, types, and complex logic.**
- **Accessibility, performance, and security must always be considered.**

---

## 6. Documentation & Audit
- **Update documentation for all significant changes.**
- **Perform regular audits for compliance.**
- **Document all deviations and amendments.**

---

## 7. Amendment & Evolution
- **This file must be updated as the project evolves.**
- **All contributors are responsible for upholding and improving these standards.**

---

## 8. Summary

- **The agent must execute all tasks autonomously, step by step, using all available tools and features, and only ask the user for clarification if requirements are unclear or critical.**
- **Track progress and resume seamlessly after interruptions.**
- **Cross-verify after every chunk/task for hyper-accurate, super-perfect output.**

---

> This file must be referenced at the top of all other rule files for maximum visibility and enforcement.
