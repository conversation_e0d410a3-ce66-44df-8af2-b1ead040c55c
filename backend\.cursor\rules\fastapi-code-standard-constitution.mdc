---
alwaysApply: true
---

# FastAPI Backend Code & Folder Constitution (Full, Exhaustive Edition)

## 1. Project Structure & Folder Standards

### 1.1. High-Level Structure
- All source code must reside in a `src/` directory at the project root.
- Each business domain or module must be a subfolder of `src/` (e.g., `src/auth`, `src/posts`).
- Each domain/module folder must contain:
  - `router.py` (API endpoints)
  - `schemas.py` (Pydantic models)
  - `models.py` (DB models)
  - `service.py` (business logic)
  - `dependencies.py` (FastAPI dependencies)
  - `constants.py` (constants and error codes)
  - `exceptions.py` (custom exceptions)
  - `config.py` (module-specific config, e.g. env vars)
  - `utils.py` (utility functions, e.g. response normalization, data enrichment)
- Shared code must be in `src/` (e.g., `src/config.py`, `src/database.py`, `src/exceptions.py`, `src/pagination.py`, `src/models.py`).
- The FastAPI app entrypoint must be `src/main.py`.
- All tests must be in a top-level `tests/` folder, mirroring the `src/` structure.
- Migrations must be in a `migrations/` or `alembic/` folder at the root.
- Templates (if any) must be in a `templates/` folder at the root.
- Requirements must be split into `requirements/base.txt`, `requirements/dev.txt`, and `requirements/prod.txt`.
- Use `.env` for environment variables, and `logging.ini` for logging config.
- All config, secrets, and credentials must be excluded from version control via `.gitignore`.

### 1.2. File Naming & Import Rules
- Use snake_case for files and folders, PascalCase for classes, and lower_snake_case for functions and variables.
- All imports must be absolute (no relative imports).
- All code must be type-annotated.
- No business logic or API code outside `src/`.
- No test code outside `tests/`.
- All imports between modules must use explicit module names (e.g., `from src.auth import constants as auth_constants`).

### 1.3. Example Structure
```
fastapi-project
├── alembic/
├── src
│   ├── auth
│   │   ├── router.py
│   │   ├── schemas.py
│   │   ├── models.py
│   │   ├── dependencies.py
│   │   ├── config.py
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   ├── service.py
│   │   └── utils.py
│   ├── aws
│   │   ├── client.py
│   │   ├── schemas.py
│   │   ├── config.py
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   └── utils.py
│   ├── posts
│   │   ├── router.py
│   │   ├── schemas.py
│   │   ├── models.py
│   │   ├── dependencies.py
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   ├── service.py
│   │   └── utils.py
│   ├── config.py
│   ├── models.py
│   ├── exceptions.py
│   ├── pagination.py
│   ├── database.py
│   └── main.py
├── tests/
│   ├── auth
│   ├── aws
│   └── posts
├── templates/
│   └── index.html
├── requirements
│   ├── base.txt
│   ├── dev.txt
│   └── prod.txt
├── .env
├── .gitignore
├── logging.ini
└── alembic.ini
```

## 2. Async, Sync, and Performance

### 2.1. Async Routes
- All endpoints must use `async def` unless blocking IO is required.
- For I/O intensive tasks (DB, HTTP, file, etc.), always use async.
- For CPU intensive tasks, use sync and run in a thread pool if needed.
- Never run blocking code in an async route; use `run_in_threadpool` if necessary.

### 2.2. Example
```python
from fastapi import APIRouter
import httpx

router = APIRouter()

@router.get("/external-data")
async def get_external_data():
    async with httpx.AsyncClient() as client:
        resp = await client.get("https://api.example.com/data")
    return resp.json()
```

## 3. Pydantic Usage & Validation

### 3.1. Excessive Use of Pydantic
- All request and response validation must use Pydantic models.
- Use Pydantic's `Field` for constraints, descriptions, and examples.
- Use custom validators for complex validation logic.
- Use Pydantic `Config` for ORM mode and schema customization.
- Create a custom global `BaseModel` for consistent serialization and shared methods.
- Decouple Pydantic `BaseSettings` for configuration, split by domain/module.

### 3.2. Example
```python
from pydantic import BaseModel, Field

class User(BaseModel):
    id: int
    name: str = Field(..., min_length=1, max_length=100, description="User's name")
```

## 4. Dependency Injection & Services

### 4.1. Beyond Dependency Injection
- All business logic must be in service classes or functions, not in routers.
- Use FastAPI's `Depends` for all dependencies.
- Chain dependencies for composability and DRYness (e.g., `get_current_user -> get_current_active_user`).
- Decouple dependencies into small, reusable functions.
- Prefer async dependencies unless strictly necessary.
- Use dependency overrides for testing.
- Dependency calls are cached per-request.

### 4.2. Example
```python
from fastapi import Depends

def get_db():
    ...

def get_current_user(db=Depends(get_db)):
    ...
```

## 5. RESTful API Design & Routing

### 5.1. RESTful Conventions
- All endpoints must be grouped by router, with tags and prefixes.
- Use RESTful conventions for all endpoints and path variables.
- Use consistent variable names in paths for dependency reuse.
- Use `response_model`, `status_code`, and `description` for all endpoints.
- Use the `responses` attribute for documenting multiple response types.
- FastAPI response serialization must be used for all responses.

### 5.2. Example
```python
from fastapi import APIRouter, status

router = APIRouter()

@router.post(
    "/endpoints",
    response_model=DefaultResponseModel,
    status_code=status.HTTP_201_CREATED,
    description="Description of the well documented endpoint",
    tags=["Endpoint Category"],
    summary="Summary of the Endpoint",
    responses={
        status.HTTP_200_OK: {
            "model": OkResponse,
            "description": "Ok Response",
        },
        status.HTTP_201_CREATED: {
            "model": CreatedResponse,
            "description": "Creates something from user request ",
        },
        status.HTTP_202_ACCEPTED: {
            "model": AcceptedResponse,
            "description": "Accepts request and handles it later",
        },
    },
)
async def documented_route():
    pass
```

## 6. Error Handling & Exceptions

### 6.1. Custom Exceptions
- Use custom exceptions for domain-specific errors.
- Raise `ValueError` in Pydantic models for client-facing validation errors.
- Use FastAPI's exception handlers for global error formatting.
- All errors must be handled with FastAPI `HTTPException` or custom exception handlers.
- Use global exception handlers for validation errors, 404s, and 500s.
- Return structured error responses with `detail` and `code` fields.
- Never expose internal errors or stack traces in production.

### 6.2. Example
```python
from fastapi import HTTPException

raise HTTPException(status_code=404, detail="Item not found", code="not_found")
```

## 7. Database, Naming, and Migrations

### 7.1. DB Naming Conventions
- Use snake_case for table and column names.
- Use singular form (e.g. `post`, `post_like`, `user_playlist`).
- Group similar tables with module prefix, e.g. `payment_account`, `payment_bill`.
- Use `_at` suffix for datetime, `_date` for date.
- Use `profile_id`, `post_id`, etc. consistently.
- Set explicit DB naming conventions for indexes, constraints, and keys.

### 7.2. Example
```python
from sqlalchemy import MetaData

POSTGRES_INDEXES_NAMING_CONVENTION = {
    "ix": "%(column_0_label)s_idx",
    "uq": "%(table_name)s_%(column_0_name)s_key",
    "ck": "%(table_name)s_%(constraint_name)s_check",
    "fk": "%(table_name)s_%(column_0_name)s_fkey",
    "pk": "%(table_name)s_pkey",
}
metadata = MetaData(naming_convention=POSTGRES_INDEXES_NAMING_CONVENTION)
```

### 7.3. Migrations
- Use Alembic for migrations; migrations must be static, revertible, and named with a date and slug.
- Generate migrations with descriptive names & slugs (e.g. `2022-08-24_post_content_idx.py`).
- Set human-readable file template for new migrations in `alembic.ini`.
- All migrations must be tested before merging.
- Use database transactions for all write operations.

### 7.4. SQL-first, Pydantic-second
- Prefer SQL for data processing, joins, and aggregations.
- Aggregate JSONs in DB for responses with nested objects.

### 7.5. Example
```python
from sqlalchemy import select, func, text

select_query = (
    select(
        ...,
        func.json_build_object(...).label("creator"),
    )
    ...
)
```

## 8. Testing & Quality

### 8.1. Async Test Client
- All tests must be async from day one (e.g., use `httpx` or `AsyncClient`).
- Use fixtures for setup/teardown.
- Use factories or fixtures for test data.
- All endpoints must have positive and negative tests.
- Use FastAPI's TestClient for API tests.
- Use test coverage tools and enforce >90% coverage.

### 8.2. Example
```python
import pytest
from async_asgi_testclient import TestClient
from src.main import app

@pytest.fixture
async def client():
    async with TestClient(app) as client:
        yield client

@pytest.mark.asyncio
async def test_create_post(client):
    resp = await client.post("/posts")
    assert resp.status_code == 201
```

## 9. Linting, Formatting, and CI

### 9.1. Linting & Formatting
- Use ruff for linting and formatting; enforce in pre-commit and CI.
- Ruff replaces black, autoflake, isort, and supports 600+ lint rules.
- All code must be auto-formatted and pass lint checks before commit.
- Use pre-commit hooks for all checks.
- All code must pass lint, type, and format checks before merge.

### 9.2. Example
```sh
#!/bin/sh -e
set -x
ruff check --fix src
ruff format src
```

### 9.3. CI
- Use GitHub Actions or similar for CI.
- All code must pass CI before merge.

## 10. Documentation

### 10.1. API Docs
- Hide API docs by default except in local/staging environments.
- Document all endpoints with `response_model`, `status_code`, `description`, and `responses`.
- All modules must have a `README.md` describing their purpose and usage.
- Use FastAPI's built-in docs and add descriptions/examples to all models and endpoints.
- Maintain a `docs/` folder for additional documentation.
- All public APIs must be documented in `README.md`.

### 10.2. Example
```python
app_configs = {"title": "My Cool API"}
if ENVIRONMENT not in SHOW_DOCS_ENVIRONMENT:
   app_configs["openapi_url"] = None
app = FastAPI(**app_configs)
```

## 11. Security & Auth

### 11.1. Authentication & Authorization
- All sensitive endpoints must require authentication.
- Use OAuth2 with JWT tokens for authentication.
- Use scopes/roles for authorization.
- Store secrets in environment variables, never in code.
- Use HTTPS in all environments except local dev.
- Sanitize all user input and use parameterized queries.
- Use CORS settings and allowed origins.
- Store session tokens securely; clear on logout/expiration.

## 12. Configuration & Environment

### 12.1. Config Management
- All config must use Pydantic `BaseSettings` and `.env` files.
- Never hardcode secrets, URLs, or credentials.
- Use separate `.env` files for dev, staging, and prod.
- All config must be documented in `README.md`.

## 13. Logging & Monitoring

### 13.1. Logging
- Use structured logging (e.g., loguru, structlog).
- Log all errors, warnings, and important events.
- Never log sensitive data.
- Integrate with monitoring/alerting tools in production.

## 14. Async, Performance & Scalability

### 14.1. Async Everywhere
- Use async everywhere except for blocking IO.
- Use connection pooling for DB and external services.
- Use background tasks for long-running jobs.
- Profile and optimize slow endpoints.
- Use pagination for all list endpoints.
- Use thread pools for sync SDKs in async routes.
- Use SQL for data aggregation and nested responses where possible.

## 15. Versioning & Deprecation

### 15.1. Versioning
- Use URL-based versioning (e.g., `/v1/` prefix).
- Deprecate old endpoints with clear warnings and timelines.
- Document all breaking changes in `CHANGELOG.md`.

## 16. Enforcement & Amendments

### 16.1. Enforcement
- This constitution is mandatory for all contributors and tools.
- All code must be reviewed and refactored to comply.
- Deviations must be documented and justified as amendments.
- Regular audits are required for compliance.
- Amendments must be proposed via PR and reviewed by maintainers.

---

**This file must be placed in /.cursor/rules/fastapi-code-standard-constitution.mdc and referenced in all planning, review, and audit processes.**

---

# Rationale & References
- This constitution is based on the best practices and conventions from [zhanymkanov/fastapi-best-practices](https://github.com/zhanymkanov/fastapi-best-practices), adapted and expanded for enforceability and clarity.
- For further reading, see the [README](https://github.com/zhanymkanov/fastapi-best-practices/blob/master/README.md) and issues section for community best practices.
