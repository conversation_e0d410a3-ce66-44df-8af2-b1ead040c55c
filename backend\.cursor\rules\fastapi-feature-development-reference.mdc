---
alwaysApply: true
---

# FastAPI Feature Development Reference Constitution (Full, Exhaustive Edition)

This constitution is the authoritative reference for all new feature development in FastAPI backend projects. It is based on the curated, community-driven knowledge in [Awesome FastAPI](https://github.com/mjhea0/awesome-fastapi) and is structured for maximum clarity, enforceability, and extensibility.

## 1. General Principles
- All new features must be designed for scalability, high performance, maintainability, and security.
- Prefer proven, community-adopted solutions and extensions over custom implementations.
- All code must be fully type-annotated, tested, and documented.
- Use async/await and non-blocking IO for all I/O-bound operations.
- Follow the project structure and code standards in the main backend constitution.
- All choices must be justified with reference to this file.

## 2. Admin Panels
### Options:
- [FastAPI Admin](https://github.com/fastapi-admin/fastapi-admin): UI for CRUD, Tortoise ORM only.
- [FastAPI Amis Admin](https://github.com/amisadmin/fastapi-amis-admin): High-performance, extensible, multi-ORM.
- [<PERSON><PERSON>lo Admin](https://github.com/piccolo-orm/piccolo_admin): Modern GUI, Piccolo ORM.
- [SQLAlchemy Admin](https://github.com/aminalaee/sqladmin): SQLAlchemy models.
- [Starlette Admin](https://github.com/jowilf/starlette-admin): Multi-ORM (SQLAlchemy, SQLModel, MongoDB, ODMantic).

### Best Pick:
- **Starlette Admin** for maximum flexibility and multi-ORM support. Use [SQLAlchemy Admin](https://github.com/aminalaee/sqladmin) if only SQLAlchemy is used. For Tortoise ORM, use [FastAPI Admin](https://github.com/fastapi-admin/fastapi-admin).

### Example:
```python
# See Starlette Admin docs for integration: https://github.com/jowilf/starlette-admin
```

## 3. Authentication & Authorization
### Options:
- [FastAPI Users](https://github.com/fastapi-users/fastapi-users): Full-featured user management, JWT, OAuth2, social logins.
- [AuthX](https://github.com/yezz123/AuthX): Customizable OAuth2 management.
- [FastAPI Auth](https://github.com/dmontagu/fastapi-auth): Pluggable OAuth2/JWT.
- [FastAPI Cloud Auth](https://github.com/tokusumi/fastapi-cloudauth): Cloud auth (AWS Cognito, Auth0, Firebase).
- [FastAPI Permissions](https://github.com/holgi/fastapi-permissions): Row-level permissions.
- [FastAPI Security](https://github.com/jacobsvante/fastapi-security): Auth as dependencies.
- [FastAPI Simple Security](https://github.com/mrtolkien/fastapi_simple_security): API key security.

### Best Pick:
- **FastAPI Users** for most use cases (JWT, OAuth2, social, extensible, production-ready).
- Use [FastAPI Cloud Auth](https://github.com/tokusumi/fastapi-cloudauth) for cloud provider auth.
- Use [FastAPI Permissions](https://github.com/holgi/fastapi-permissions) for advanced row-level permissions.

### Example:
```python
# See FastAPI Users docs: https://fastapi-users.github.io/fastapi-users/
```

## 4. Database Layer (ORMs, ODMs, Query Builders)
### ORMs:
- [SQLModel](https://sqlmodel.tiangolo.com/): Async, Pydantic+SQLAlchemy, best for new projects.
- [Tortoise ORM](https://tortoise.github.io/): Async, Django-like, easy migrations.
- [ormar](https://collerek.github.io/ormar/): Async, Pydantic validation, Alembic migrations.
- [SQLAlchemy](https://www.sqlalchemy.org/): Most mature, sync/async, huge ecosystem.
- [GINO](https://github.com/python-gino/gino): Async, SQLAlchemy core.
- [Piccolo](https://github.com/piccolo-orm/piccolo): Async, batteries-included.
- [Prisma Client Python](https://github.com/RobertCraigie/prisma-client-py): Type-safe, multi-DB.

### ODMs (MongoDB):
- [Beanie](https://github.com/BeanieODM/beanie): Async, Pydantic, migrations.
- [ODMantic](https://art049.github.io/odmantic/): Async, Pydantic.
- [MongoEngine](http://mongoengine.org/): Mature, sync.

### Query Builders:
- [Databases](https://github.com/encode/databases): Async SQL builder.
- [PyPika](https://github.com/kayak/pypika): SQL query builder.

### Best Pick:
- **SQLModel** for new SQL projects (async, Pydantic, SQLAlchemy, Alembic support).
- **Beanie** for MongoDB (async, Pydantic, migrations).
- Use [Databases](https://github.com/encode/databases) for async SQL without ORM.

### Example:
```python
# See SQLModel docs: https://sqlmodel.tiangolo.com/tutorial/
```

## 5. Developer Tools & Productivity
### Options:
- [FastAPI Code Generator](https://github.com/koxudaxi/fastapi-code-generator): OpenAPI-driven codegen.
- [FastAPI MVC](https://github.com/fastapi-mvc/fastapi-mvc): Large, production-grade APIs.
- [Manage FastAPI](https://github.com/ycd/manage-fastapi): CLI for project scaffolding.
- [FastAPI Profiler](https://github.com/sunhailin-Leo/fastapi_profiler): Performance profiling.
- [FastAPI Versioning](https://github.com/DeanWay/fastapi-versioning): API versioning.

### Best Pick:
- Use **FastAPI MVC** for large, maintainable codebases.
- Use **FastAPI Code Generator** for OpenAPI-first development.
- Use **Manage FastAPI** for scaffolding and management.

## 6. Utilities & Extensions
### Caching:
- [FastAPI Cache](https://github.com/long2ice/fastapi-cache): Redis, Memcached, DynamoDB, in-memory.
- [FastAPI Cache (comeuplater)](https://github.com/comeuplater/fastapi_cache): Lightweight cache.

### Rate Limiting:
- [FastAPI Limiter](https://github.com/long2ice/fastapi-limiter): Redis-based.
- [SlowApi](https://github.com/laurents/slowapi): Flask-Limiter port.

### Pagination:
- [FastAPI Pagination](https://github.com/uriyyo/fastapi-pagination): Scalable, flexible.

### Feature Flags:
- [FastAPI FeatureFlags](https://github.com/Pytlicek/fastapi-featureflags): Simple feature flags.

### Background Tasks:
- [Celery](https://docs.celeryq.dev/en/stable/): Distributed jobs.
- [FastAPI built-in background tasks](https://fastapi.tiangolo.com/tutorial/background-tasks/): Simple jobs.

### Real-Time & WebSockets:
- [FastAPI Websocket Pub/Sub](https://github.com/authorizon/fastapi_websocket_pubsub): Scalable pub/sub.
- [FastAPI Websocket RPC](https://github.com/authorizon/fastapi_websocket_rpc): RPC over websockets.
- [FastAPI SocketIO](https://github.com/pyropy/fastapi-socketio): Socket.IO integration.

### Email:
- [FastAPI Mail](https://github.com/sabuhish/fastapi-mail): Email sending.

### Monitoring & Observability:
- [Prometheus FastAPI Instrumentator](https://github.com/trallnag/prometheus-fastapi-instrumentator): Prometheus metrics.
- [OpenTelemetry FastAPI Instrumentation](https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-fastapi): Tracing.
- [Apitally](https://github.com/apitally/apitally-py): API analytics.

### Best Picks:
- **FastAPI Cache** for caching.
- **FastAPI Limiter** for rate limiting.
- **FastAPI Pagination** for pagination.
- **Celery** for distributed background jobs.
- **Prometheus FastAPI Instrumentator** for metrics.
- **OpenTelemetry** for tracing.

## 7. Testing & Quality
### Options:
- [pytest](https://docs.pytest.org/): Standard for Python testing.
- [httpx](https://www.python-httpx.org/): Async HTTP client.
- [Schemathesis](https://schemathesis.readthedocs.io/): OpenAPI-driven testing.
- [Hypothesis](https://hypothesis.readthedocs.io/): Property-based testing.

### Best Pick:
- Use **pytest** for all tests.
- Use **httpx** for async test clients.
- Use **Schemathesis** and **Hypothesis** for advanced API testing.

## 8. Documentation & Client SDKs
### Options:
- FastAPI's built-in OpenAPI docs.
- [FastAPI Client Generator](https://github.com/dmontagu/fastapi_client): Generate API clients.
- [openapi-python-client](https://github.com/openapi-generators/openapi-python-client): Modern Python clients from OpenAPI.

### Best Pick:
- Use **FastAPI's built-in docs** for API documentation.
- Use **openapi-python-client** for client SDKs.

## 9. Deployment, Hosting, and Docker
### Docker Images:
- [uvicorn-gunicorn-fastapi-docker](https://github.com/tiangolo/uvicorn-gunicorn-fastapi-docker): Production-ready.
- [inboard](https://github.com/br3ndonland/inboard): FastAPI Docker images.

### Serverless:
- [Mangum](https://mangum.io/): AWS Lambda.
- [Google Cloud Run](https://cloud.google.com/run): Serverless containers.
- [Vercel](https://vercel.com/): Serverless deployments.

### PaaS/IaaS:
- [Heroku](https://www.heroku.com/): Easy deploy.
- [Fly](https://fly.io/): Global apps.
- [Deta](https://www.deta.sh/): Free hosting.
- [AWS EC2](https://aws.amazon.com/ec2/), [Google Compute Engine](https://cloud.google.com/compute), [Digital Ocean](https://www.digitalocean.com/), [Linode](https://www.linode.com/): IaaS.

### Best Pick:
- Use **uvicorn-gunicorn-fastapi-docker** for Dockerized deployments.
- Use **Mangum** for AWS Lambda, **Google Cloud Run** for serverless containers.
- Use **Heroku** or **Fly** for PaaS.

## 10. Project Templates & Boilerplates
### Options:
- [Full Stack FastAPI Template](https://github.com/fastapi/full-stack-fastapi-template): FastAPI, React, SQLModel, PostgreSQL, Docker, CI/CD.
- [FastAPI Nano](https://github.com/rednafi/fastapi-nano): Minimal, factory pattern.
- [cookiecutter-fastapi](https://github.com/arthurhenrique/cookiecutter-fastapi): ML, Poetry, Azure Pipelines, pytest.
- [fastapi-gino-arq-uvicorn](https://github.com/leosussan/fastapi-gino-arq-uvicorn): Async REST API, GINO, Arq, Uvicorn, Redis, PostgreSQL.
- [fastapi-alembic-sqlmodel-async](https://github.com/jonra1993/fastapi-alembic-sqlmodel-async): Async SQLModel, Alembic.
- [fastapi-starter-project](https://github.com/mirzadelic/fastapi-starter-project): SQLModel, Alembic, Pytest, Docker, GitHub Actions.
- [Full Stack FastAPI and MongoDB](https://github.com/mongodb-labs/full-stack-fastapi-mongodb): FastAPI, MongoDB, Docker, Celery, React.

### Best Pick:
- Use **Full Stack FastAPI Template** for most new projects.
- Use **FastAPI Nano** for minimal, rapid prototyping.
- Use **fastapi-alembic-sqlmodel-async** for async SQLModel projects.

## 11. Real-World Example Projects
- [Awesome FastAPI Projects](https://github.com/Kludex/awesome-fastapi-projects): Curated list of real-world projects.
- [RealWorld Example App - mongo](https://github.com/markqiu/fastapi-mongodb-realworld-example-app)
- [RealWorld Example App - postgres](https://github.com/nsidnev/fastapi-realworld-example-app)
- [FastAPI CRUD Example (Async)](https://github.com/testdrivenio/fastapi-crud-async)
- [FastAPI with Observability](https://github.com/Blueswen/fastapi-observability)
- [FastAPI with Celery, RabbitMQ, and Redis](https://github.com/GregaVrbancic/fastapi-celery)

## 12. Learning Resources & Community Knowledge
- [FastAPI Documentation](https://fastapi.tiangolo.com/): Official docs.
- [TestDriven.io FastAPI](https://testdriven.io/blog/topics/fastapi/): Production-ready API articles.
- [FastAPI Best Practices](https://github.com/zhanymkanov/fastapi-best-practices): Architecture, code, workflow.
- [Async SQLAlchemy with FastAPI](https://stribny.name/posts/fastapi-asyncalchemy/): Async SQLAlchemy.
- [Build and Secure an API in Python with FastAPI](https://blog.yezz.me/blog/Build-and-Secure-an-API-in-Python-with-FastAPI): Security.
- [Developing and Testing an Asynchronous API with FastAPI and Pytest](https://testdriven.io/blog/fastapi-crud/): Async API, TDD.
- [Implementing FastAPI Services – Abstraction and Separation of Concerns](https://camillovisini.com/coding/abstracting-fastapi-services): Service structure.
- [Running FastAPI applications in production](https://stribny.name/posts/fastapi-production/): Gunicorn, systemd.
- [Serving Machine Learning Models with FastAPI in Python](https://medium.com/@8B_EC/tutorial-serving-machine-learning-models-with-fastapi-in-python-c1a27319c459): ML APIs.
- [Using Hypothesis and Schemathesis to Test FastAPI](https://testdriven.io/blog/fastapi-hypothesis/): Property-based testing.
- [PyConBY 2020: Serve ML models easily with FastAPI](https://www.youtube.com/watch?v=z9K5pwb0rt8): Video.
- [Modern APIs with FastAPI and Python](https://training.talkpython.fm/courses/modern-fastapi-apis): Course.
- [Full Web Apps with FastAPI Course](https://training.talkpython.fm/courses/full-html-web-applications-with-fastapi): Course.

---

**All new features must justify their choices with reference to this file. This file must be referenced in all planning, review, and audit processes for new backend features.**

**Primary source: [Awesome FastAPI](https://github.com/mjhea0/awesome-fastapi)**
