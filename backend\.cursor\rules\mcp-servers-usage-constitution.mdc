---
alwaysApply: true
---

# Sequential Thinking MCP Server Usage Rules

**When to Use:**
- For breaking down complex problems into manageable steps.
- When planning or designing processes that may require iterative revisions or course corrections.
- For tasks where the full scope is unclear at the start and may evolve.
- When you need to maintain context over multiple steps or stages.
- For dynamic, reflective problem-solving that may require branching or revisiting previous steps.

**Features:**
- Supports multiple reasoning strategies (linear, chain of thought, react, rewoo, scratchpad, self-ask, self-consistency, step back, tree of thoughts, trilemma).
- Allows revision and branching of thought processes.
- Enables hypothesis generation and verification.
- Provides flexible, stepwise analysis and solution building.
- Integrates with compatible agentic workflows and IDEs.

**Available Tools:**

- `sequentialthinking`:
  - **Purpose:**
    - Enables dynamic, reflective problem-solving by breaking down complex problems into a sequence of "thoughts" (steps).
    - Supports revision, branching, and context maintenance across multiple steps.
    - Provides intelligent recommendations for next steps and tool usage (if integrated with tool suggestion extensions).
  - **Arguments:**
    - `thought` (string, required): The current thinking step or analysis.
    - `next_thought_needed` (boolean, required): Whether another thought step is needed.
    - `thought_number` (integer, required): Current step number in the sequence.
    - `total_thoughts` (integer, required): Estimated total steps needed.
    - `is_revision` (boolean, optional): If this step revises a previous one.
    - `revises_thought` (integer, optional): Which step is being revised.
    - `branch_from_thought` (integer, optional): If branching, which step is the branch point.
    - `branch_id` (string, optional): Identifier for the current branch.
    - `needs_more_thoughts` (boolean, optional): If more steps are needed.
    - `current_step` (object, optional): Description, recommended tools, expected outcome, and next step conditions.
    - `previous_steps` (array, optional): Steps already recommended.
    - `remaining_steps` (array, optional): High-level descriptions of upcoming steps.
  - **Best Practices:**
    - Use for any problem that benefits from stepwise, transparent reasoning (e.g., planning, debugging, research, design).
    - Regularly review and revise previous steps as new information emerges.
    - Use branching to explore alternative solutions or hypotheses.
    - Document each step clearly for reproducibility and collaboration.
    - Stay within the free tier: all features listed here are available under the MIT license and do not require payment.

**General Best Practices:**
- Use the appropriate reasoning strategy for your problem (e.g., chain of thought for stepwise logic, tree of thoughts for exploring alternatives).
- Regularly review and revise previous steps as new information emerges.
- Document your reasoning steps for transparency and reproducibility.

# Postgres MCP Server Usage Rules

**When to Use:**
- For secure, read-only access to PostgreSQL databases from AI agents or LLMs.
- When you need to inspect database schemas, run analytical or reporting queries, or explore data without risk of modification.
- For automating data analysis, generating reports, or enabling natural language database queries in your workflow.
- When you want to bridge AI assistants with live, up-to-date database information.

**Features:**
- Enforces read-only SQL execution for maximum data safety.
- Allows schema inspection (tables, columns, types, keys, relationships).
- Supports natural language-driven query formulation and result presentation.
- Integrates with MCP-compatible clients (e.g., Claude Desktop, Cursor IDE).
- Secure connection string configuration and environment variable support.
- Automatic discovery of database metadata for AI context.

**Available Tools:**

- `query`:
  - **Purpose:**
    - Execute a read-only SQL query against the connected PostgreSQL database and return the results.
    - Used for data exploration, reporting, and analytics without risk of data modification.
  - **Arguments:**
    - `sql` (string, required): The SQL SELECT query to execute.
  - **Usage:**
    - Use to fetch data, run analytics, or inspect table contents in a safe, read-only manner.
    - Always validate and sanitize queries to avoid performance issues.
  - **Best Practices:**
    - Use only SELECT statements; DML/DDL is not permitted in read-only mode.
    - Limit result set size for large tables (e.g., use LIMIT clause).
    - Review query plans for complex queries to avoid heavy loads.
    - Use for reporting, dashboarding, and data-driven agentic workflows.

**General Best Practices:**
- Always use restricted (read-only) mode for production or sensitive databases.
- Use a dedicated database user with minimal privileges for MCP connections.
- Review tool arguments and outputs carefully; avoid running heavy operations on production without validation.
- Document and audit all tool usage for compliance and troubleshooting.

# Filesystem MCP Server Usage Rules

**When to Use:**
- For secure, programmatic access to local or remote files and directories from AI agents or LLMs.
- When you need to list, read, or search files as part of an automated workflow or data pipeline.
- For enabling LLMs to fetch, analyze, or summarize file contents (e.g., code, logs, documents).
- When integrating file-based resources into agentic or multi-step AI tasks.

**Features:**
- Supports directory listing, file reading, and file searching operations.
- Configurable access scope (restrict to specific directories for safety).
- Enforces access controls and can be run in read-only mode for security.
- Integrates with MCP-compatible clients (e.g., Claude Desktop, Cursor IDE).
- Provides structured error handling and clear tool documentation for agents.
- Can be containerized for portable, secure deployment.

**Available Tools:**

- `read_file`:
  - **Purpose:** Read the complete contents of a file (UTF-8 encoding).
  - **Arguments:**
    - `path` (string, required): Path to the file to read.
  - **Usage:** Use to fetch the full content of a single file for analysis, summarization, or code review.
  - **Best Practices:**
    - Only use on files within allowed directories.
    - Avoid reading very large files unless necessary (may impact performance).
    - Validate file paths to prevent unauthorized access.

- `read_multiple_files`:
  - **Purpose:** Read the contents of multiple files simultaneously.
  - **Arguments:**
    - `paths` (array of strings, required): List of file paths to read.
  - **Usage:** Use to efficiently gather content from several files at once (e.g., for codebase analysis or batch processing).
  - **Best Practices:**
    - Handle partial failures gracefully (some files may not be readable).
    - Limit the number of files per call to avoid resource exhaustion.

- `list_directory`:
  - **Purpose:** List the contents of a directory, showing files and subdirectories.
  - **Arguments:**
    - `path` (string, required): Path to the directory to list.
  - **Usage:** Use to explore directory structure, find files, or enumerate available resources.
  - **Best Practices:**
    - Use as a first step before reading or editing files.
    - Restrict to allowed directories for security.

- `list_directory_with_sizes`:
  - **Purpose:** List directory contents with file sizes and types.
  - **Arguments:**
    - `path` (string, required): Path to the directory.
    - `sortBy` (string, optional): Sort by 'name' or 'size'.
  - **Usage:** Use to analyze storage usage or identify large files.
  - **Best Practices:**
    - Use sorting to quickly find the largest files or directories.
    - Combine with `get_file_info` for detailed metadata.

- `directory_tree`:
  - **Purpose:** Get a recursive tree view of files and directories as a JSON structure.
  - **Arguments:**
    - `path` (string, required): Root directory for the tree.
  - **Usage:** Use to visualize or analyze the full directory hierarchy.
  - **Best Practices:**
    - Limit depth or scope for very large trees to avoid performance issues.
    - Use for project overviews or dependency mapping.

- `search_files`:
  - **Purpose:** Recursively search for files and directories matching a pattern.
  - **Arguments:**
    - `path` (string, required): Starting directory.
    - `pattern` (string, required): Search pattern (case-insensitive, partial match).
    - `excludePatterns` (array of strings, optional): Patterns to exclude.
  - **Usage:** Use to quickly locate files by name or extension across large codebases.
  - **Best Practices:**
    - Use specific patterns to reduce result set size.
    - Exclude irrelevant directories (e.g., node_modules) for speed.

- `get_file_info`:
  - **Purpose:** Retrieve detailed metadata about a file or directory.
  - **Arguments:**
    - `path` (string, required): Path to the file or directory.
  - **Usage:** Use to get size, timestamps, permissions, and type (file/dir).
  - **Best Practices:**
    - Use before reading or editing files to check size and type.
    - Combine with `list_directory_with_sizes` for comprehensive audits.

**General Best Practices (Filesystem Tools):**
- Always restrict operations to explicitly allowed directories.
- Run in read-only mode unless write access is absolutely required (not covered here).
- Validate all user inputs and file paths to prevent directory traversal or injection attacks.
- Monitor resource usage and set limits for large/batch operations.
- Document tool usage and expected arguments for all users and agents.
- Use structured logging and error handling for troubleshooting and auditing.

# XMind MCP Server Usage Rules

**When to Use:**
- For programmatic access to XMind mind maps from AI agents or LLMs.
- When you need to search, analyze, or extract structured information from mind map files.
- For automating mind map processing, summarization, or integration into workflows.
- When enabling LLMs to navigate, visualize, or manipulate hierarchical data in XMind format.

**Features:**
- Supports fuzzy search, hierarchical navigation, and extraction of nodes and relationships.
- Can analyze, summarize, and convert mind map content to various formats (e.g., JSON, markdown).
- Enables batch processing and cross-file analysis for large mind map collections.
- Integrates with MCP-compatible clients (e.g., Cursor IDE, Claude Desktop).
- Provides structured error handling and clear tool documentation for agents.
- Can be containerized for secure, portable deployment.

**Available Tools:**

- `read_xmind`:
  - **Purpose:** Parse and analyze a single XMind file, extracting the complete mind map structure in JSON format.
  - **Arguments:**
    - `path` (string, required): Path to the .xmind file.
  - **Usage:** Use to obtain the full structure, nodes, relationships, and callouts from a mind map for further analysis or visualization.
  - **Best Practices:**
    - Use as a first step to understand the content and structure of a mind map.
    - Validate file paths to ensure access is restricted to trusted directories.

- `list_xmind_directory`:
  - **Purpose:** Recursively scan a directory for .xmind files, optionally filtering or grouping results.
  - **Arguments:**
    - `directory` (string, optional): Directory to scan (defaults to allowed root).
  - **Usage:** Use to discover all XMind files in a directory tree for batch processing or analysis.
  - **Best Practices:**
    - Restrict scanning to trusted directories to avoid unauthorized access.
    - Use filters to narrow results if working with large collections.

- `read_multiple_xmind_files`:
  - **Purpose:** Process and analyze multiple XMind files simultaneously, enabling comparison and cross-file analysis.
  - **Arguments:**
    - `paths` (array of strings, required): List of .xmind file paths.
  - **Usage:** Use to compare content, merge themes, or generate consolidated summaries across files.
  - **Best Practices:**
    - Limit the number of files per call to avoid performance issues.
    - Handle partial failures gracefully (some files may be unreadable).

- `search_xmind_files`:
  - **Purpose:** Recursively search for .xmind files by partial name matching within a directory.
  - **Arguments:**
    - `pattern` (string, required): Search text to match in file names.
    - `directory` (string, optional): Directory to search (defaults to allowed root).
  - **Usage:** Use to quickly locate mind maps by name or keyword.
  - **Best Practices:**
    - Use specific patterns to reduce result set size.
    - Restrict search to trusted directories for security.

- `extract_node`:
  - **Purpose:** Smart fuzzy path matching to extract a specific node and its subtree from a mind map.
  - **Arguments:**
    - `path` (string, required): Path to the .xmind file.
    - `searchQuery` (string, required): Text to search in node paths (flexible matching).
  - **Usage:** Use to extract a node and all its children for focused analysis or export.
  - **Best Practices:**
    - Use clear, descriptive queries for best results.
    - Validate file paths and queries to avoid errors.

- `extract_node_by_id`:
  - **Purpose:** Directly extract a node and its subtree using its unique XMind ID.
  - **Arguments:**
    - `path` (string, required): Path to the .xmind file.
    - `nodeId` (string, required): Unique identifier of the node.
  - **Usage:** Use for fast, precise access to known nodes (e.g., from previous search results).
  - **Best Practices:**
    - Ensure node IDs are valid and correspond to the correct file.
    - Use in conjunction with `search_nodes` or `read_xmind` for best workflow.

- `search_nodes`:
  - **Purpose:** Advanced search for nodes within a mind map using multiple criteria (title, notes, labels, callouts, tasks).
  - **Arguments:**
    - `path` (string, required): Path to the .xmind file.
    - `query` (string, required): Search text.
    - `searchIn` (array of strings, optional): Fields to search in (e.g., ['title', 'notes']).
    - `caseSensitive` (boolean, optional): Case sensitivity flag.
    - `taskStatus` (string, optional): Filter by task status ('todo' or 'done').
  - **Usage:** Use to find nodes by content, label, or task status for targeted extraction or analysis.
  - **Best Practices:**
    - Combine multiple search fields for more precise results.
    - Use task status filtering to manage project tasks within mind maps.

**General Best Practices:**
- Restrict access to trusted directories containing XMind files.
- Validate all file paths and user inputs to prevent unauthorized access or injection attacks.
- Use structured logging and monitoring for troubleshooting and auditing.
- Document all available tools and their expected inputs/outputs for both agents and users.
- Regularly update the server and dependencies for security and compatibility.
- Test with a variety of XMind files to ensure robust parsing and extraction.

# Memory MCP Server Usage Rules

**When to Use:**
- For enabling AI agents or LLMs to store, retrieve, and manage structured or unstructured memory across sessions.
- When you need persistent context, knowledge graphs, or long-term state for agentic workflows.
- For automating knowledge capture, entity/relation tracking, or context-aware reasoning.
- When integrating memory with other MCP tools (e.g., filesystem, database) to enhance multi-step or multi-agent tasks.

**Features:**
- Supports creation, update, and deletion of entities, relations, and observations in a knowledge graph.
- Enables flexible, schema-less storage of facts, events, and context for AI agents.
- Provides search, retrieval, and structured querying of memory content.
- Integrates with MCP-compatible clients and can be containerized for secure deployment.
- Offers access controls and can be configured for multi-user or multi-agent scenarios.
- Structured error handling and clear tool documentation for agents.

**Available Tools:**

- `create_entities`:
  - **Purpose:** Create one or more new entities (nodes) in the knowledge graph, each with a name, type, and optional observations (facts or notes).
  - **Arguments:**
    - `entities` (array, required): Each entity includes `name` (string), `entityType` (string), and `observations` (array of strings, optional).
  - **Usage:** Use to add new concepts, objects, or people to the memory graph, optionally with initial facts or context.
  - **Best Practices:**
    - Use clear, unique names and types for entities to avoid confusion.
    - Add initial observations to provide context for new entities.

- `create_relations`:
  - **Purpose:** Create one or more relationships (edges) between entities in the knowledge graph.
  - **Arguments:**
    - `relations` (array, required): Each relation includes `from` (string), `to` (string), and `relationType` (string).
  - **Usage:** Use to connect entities (e.g., "Alice" is a "friend" of "Bob").
  - **Best Practices:**
    - Use descriptive relation types (e.g., "parent", "works_at").
    - Ensure both entities exist before creating a relation.

- `add_obversions`:
  - **Purpose:** Add new observations (facts, notes, or context) to existing entities in the knowledge graph.
  - **Arguments:**
    - `observations` (array, required): Each includes `entityName` (string) and `contents` (array of strings).
  - **Usage:** Use to enrich entities with new information over time.
  - **Best Practices:**
    - Add observations incrementally as new facts are discovered.
    - Keep observations concise and relevant.

- `delete_entities`:
  - **Purpose:** Delete one or more entities (nodes) and their associated relations from the knowledge graph.
  - **Arguments:**
    - `entityNames` (array of strings, required): Names of entities to delete.
  - **Usage:** Use to remove obsolete or incorrect entities and clean up the graph.
  - **Best Practices:**
    - Double-check before deleting, as this removes all related data.
    - Use for maintenance and pruning of the knowledge base.

- `delete_obversions`:
  - **Purpose:** Delete specific observations from entities in the knowledge graph.
  - **Arguments:**
    - `deletions` (array, required): Each includes `entityName` (string) and `observations` (array of strings to delete).
  - **Usage:** Use to remove outdated or incorrect facts from entities.
  - **Best Practices:**
    - Use for targeted cleanup without deleting the entire entity.
    - Keep a record of deleted observations if needed for audit.

- `delete_relations`:
  - **Purpose:** Delete specific relationships (edges) between entities in the knowledge graph.
  - **Arguments:**
    - `relations` (array, required): Each includes `from` (string), `to` (string), and `relationType` (string).
  - **Usage:** Use to remove outdated or incorrect connections between entities.
  - **Best Practices:**
    - Use for precise graph editing; avoid deleting entities if only the relation is wrong.

- `read_graph`:
  - **Purpose:** Retrieve the entire knowledge graph structure, including all entities, relations, and observations.
  - **Arguments:**
    - `random_string` (string, required): Dummy parameter for protocol compliance.
  - **Usage:** Use to export, visualize, or audit the current state of memory.
  - **Best Practices:**
    - Use for backups, audits, or to provide a full context to agents.
    - Be aware of graph size for large knowledge bases.

- `search_nodes`:
  - **Purpose:** Search for nodes (entities) in the knowledge graph based on a query string.
  - **Arguments:**
    - `query` (string, required): Search text to match against entity names, types, and observation content.
  - **Usage:** Use to find relevant entities or facts quickly.
  - **Best Practices:**
    - Use specific queries for precise results; broad queries for exploration.

- `open_nodes`:
  - **Purpose:** Retrieve detailed information about specific entities (nodes) by their names.
  - **Arguments:**
    - `names` (array of strings, required): Names of entities to retrieve.
  - **Usage:** Use to get all observations, relations, and metadata for selected entities.
  - **Best Practices:**
    - Use after `search_nodes` to drill down into specific results.

**General Best Practices:**
- Use clear, consistent naming for entities and relations.
- Regularly review and prune obsolete or redundant memory to optimize performance.
- Restrict access to trusted agents and validate all inputs to prevent injection or data leakage.
- Document all available tools, entity types, and expected inputs/outputs for both agents and users.
- Monitor memory usage and set up alerts for unusual access or modification patterns.
- Combine with other MCP servers (e.g., filesystem, memory) for richer, context-aware workflows.

# Firecrawl MCP Server Usage Rules

**When to Use:**
- For web scraping, crawling, and content extraction from websites directly via AI agents or LLMs.
- When you need to search, extract, or batch process web data for research, monitoring, or data collection.
- For automating deep research, structured data extraction, or multi-page crawling tasks.
- When integrating live web content into agentic workflows, data pipelines, or knowledge bases.

**Features:**
- Supports single-page scraping, batch scraping, crawling, mapping, and web search.
- Enables structured data extraction using LLMs, deep research, and LLMs.txt generation for domains.
- Built-in rate limiting, retry logic, and credit usage monitoring for robust, scalable operation (within free tier limits).
- Cloud and self-hosted deployment options, with API key-based authentication (free tier only).
- Integrates with MCP-compatible clients (Cursor, Claude Desktop, VS Code, etc.).
- Provides comprehensive logging, error handling, and progress tracking.

**Available Tools:**

- `firecrawl_scrape`:
  - **Purpose:** Scrape content from a single URL with advanced options.
  - **Arguments:**
    - `url` (string, required): The URL to scrape.
    - `formats` (array, optional): Output formats (e.g., ["markdown"], ["html"]).
    - `onlyMainContent` (boolean, optional): Extract only the main content.
    - `waitFor` (number, optional): Time in ms to wait for dynamic content.
    - `timeout` (number, optional): Max time in ms to wait for the page.
    - `mobile` (boolean, optional): Use mobile viewport.
    - `includeTags` (array, optional): HTML tags to include.
    - `excludeTags` (array, optional): HTML tags to exclude.
    - `skipTlsVerification` (boolean, optional): Skip TLS verification.
  - **Usage:** Use for single-page content extraction when you know the exact URL.
  - **Best Practices:**
    - Do not use for multiple URLs (use batch scrape or crawl).
    - Use `onlyMainContent` for cleaner results.
    - Adjust `waitFor` for dynamic sites.

- `firecrawl_map`:
  - **Purpose:** Map a website to discover all indexed URLs on the site.
  - **Arguments:**
    - `url` (string, required): The base URL to map.
    - `search` (string, optional): Filter URLs by search term.
    - `ignoreSitemap` (boolean, optional): Skip sitemap discovery.
    - `sitemapOnly` (boolean, optional): Use only sitemap.
    - `includeSubdomains` (boolean, optional): Include subdomains.
    - `limit` (number, optional): Max URLs to return.
  - **Usage:** Use to discover URLs before scraping or crawling.
  - **Best Practices:**
    - Use before batch scraping or crawling large sites.
    - Limit results for large domains.

- `firecrawl_crawl`:
  - **Purpose:** Start an asynchronous crawl job to extract content from all pages of a site or section.
  - **Arguments:**
    - `url` (string, required): Starting URL for the crawl.
    - `maxDepth` (number, optional): Max link depth to crawl.
    - `limit` (number, optional): Max number of pages to crawl.
    - `allowExternalLinks` (boolean, optional): Allow crawling external domains.
    - `deduplicateSimilarURLs` (boolean, optional): Remove similar URLs.
    - `ignoreSitemap` (boolean, optional): Skip sitemap discovery.
    - `scrapeOptions` (object, optional): Scraping options for each page.
  - **Usage:** Use for multi-page extraction when you need comprehensive coverage.
  - **Best Practices:**
    - Limit depth and page count to avoid large responses.
    - Use deduplication for efficiency.
    - Not recommended for single pages (use scrape).

- `firecrawl_check_crawl_status`:
  - **Purpose:** Check the status and progress of a crawl job.
  - **Arguments:**
    - `id` (string, required): The crawl job ID.
  - **Usage:** Use to monitor and retrieve results from ongoing or completed crawl jobs.
  - **Best Practices:**
    - Poll periodically for status updates.
    - Use operation IDs from crawl responses.

- `firecrawl_search`:
  - **Purpose:** Search the web and optionally extract content from search results. Returns relevant snippets and URLs from web pages.
  - **Arguments:**
    - `query` (string, required): Search term to look up on the web.
    - `limit` (number, optional): Max number of results to return (default: 5).
    - `lang` (string, optional): Language code for search results (default: en).
    - `country` (string, optional): Country code for search results (default: us).
    - `scrapeOptions` (object, optional): Content formats to extract from search results.
  - **Usage:** Use for finding specific information across multiple websites, especially when the exact source is unknown.
  - **Best Practices:**
    - Plan queries to maximize information per call due to rate limits (see below).
    - Use for open-ended research, not for scraping a known page (use scrape).

- `firecrawl_extract`:
  - **Purpose:** Extract structured information from web pages using LLM capabilities.
  - **Arguments:**
    - `urls` (array, required): List of URLs to extract from.
    - `prompt` (string, optional): Custom prompt for LLM extraction.
    - `systemPrompt` (string, optional): System prompt for LLM.
    - `schema` (object, optional): JSON schema for structured extraction.
    - `allowExternalLinks` (boolean, optional): Allow extraction from external links.
    - `enableWebSearch` (boolean, optional): Enable web search for context.
    - `includeSubdomains` (boolean, optional): Include subdomains.
  - **Usage:** Use for extracting structured data (e.g., product info) from one or more pages.
  - **Best Practices:**
    - Provide a clear schema and prompt for best results.
    - Use for targeted data extraction, not full-page content.
    - Not recommended for general scraping (use scrape or crawl).

- `firecrawl_deep_research`:
  - **Purpose:** Conduct deep web research on a query using intelligent crawling, search, and LLM analysis.
  - **Arguments:**
    - `query` (string, required): The research question or topic to explore.
    - `maxDepth` (number, optional): Maximum recursive depth for crawling/search (default: 3).
    - `timeLimit` (number, optional): Time limit in seconds for the research session (default: 120).
    - `maxUrls` (number, optional): Maximum number of URLs to analyze (default: 50).
  - **Usage:** Use for complex research questions requiring multiple sources and in-depth analysis.
  - **Best Practices:**
    - Use for broad, multi-source research tasks.
    - Limit depth and URLs for performance and token safety.

- `firecrawl_generate_llmstxt`:
  - **Purpose:** Generate a standardized llms.txt (and optionally llms-full.txt) file for a given domain, defining how LLMs should interact with the site.
  - **Arguments:**
    - `url` (string, required): The base URL of the website to analyze.
    - `maxUrls` (number, optional): Max number of URLs to include (default: 10).
    - `showFullText` (boolean, optional): Whether to include llms-full.txt contents in the response.
  - **Usage:** Use to create machine-readable permission guidelines for AI models.
  - **Best Practices:**
    - Use for compliance and AI permission management.
    - Limit URLs for large domains.

**General Best Practices:**
- Use scrape for single pages, map to discover URLs, crawl for multi-page extraction (with limits), search for open-ended queries, extract for structured data, deep_research for complex topics, and generate_llmstxt for AI compliance.
- Limit crawl depth and number of pages to avoid token overflow and performance issues.
- Monitor API credit usage and configure retry/backoff settings for reliability (within free tier limits).
- Validate all URLs and user inputs to prevent abuse or injection attacks.
- Regularly update the server and dependencies for security and compatibility.
- Combine with other MCP servers (e.g., memory, filesystem) for richer, context-aware workflows.

**Special Rate Limit Rule for `firecrawl_search`:**

- The `firecrawl_search` tool has a strict rate limit: **you can only make 5 successful calls within any 30-second window**. If you attempt a 6th call within 30 seconds, you will receive an error and must wait until the window resets.
- **Best Practices:**
  - Plan your queries carefully to maximize the information retrieved in each call. Where possible, batch related information requests into a single search.
  - For multi-step research or data extraction, try to complete all related tasks for a topic within the results of a single call, or within the 5-call window.
  - If you reach the 5-call limit, use the time to process, analyze, and act on the data retrieved while waiting for the rate limit to reset.
  - Avoid unnecessary or redundant calls; always check if the required data can be obtained from previous results before making a new request.
  - For workflows requiring more than 5 calls, structure your logic to pause and resume after the 30-second cooldown, ensuring smooth, uninterrupted progress.
  - Document this limitation for all users and agents to prevent confusion and errors during high-frequency research tasks.

# @21st-dev/magic MCP Server Usage Rules

**When to Use:**
- For generating beautiful, modern UI components instantly via natural language descriptions.
- When you need to rapidly prototype, design, or enhance frontend interfaces without manual coding.
- For automating the creation of buttons, forms, navigation bars, cards, and other UI elements.
- When integrating AI-powered UI generation into agentic workflows or IDEs (Cursor, Windsurf, VSCode, Cline).

**Features:**
- AI-driven UI component generation from natural language prompts (e.g., "/ui create a modern navbar").
- Access to a large, customizable component library inspired by 21st.dev.
- Real-time preview and seamless integration with supported IDEs (within free tier request limits).
- TypeScript and modern framework support for type-safe, production-ready code.
- SVGL integration for professional brand assets and logos (free tier only).
- API key-based authentication with a free tier (limited requests; paid plans not included here).
- Fast response times and well-structured, editable code output (HTML/CSS/React/TSX).

**Available Tools:**

- `logo_search`:
  - **Purpose:** Search and return logos in the specified format (JSX, TSX, SVG). Supports single and multiple logo searches with category filtering and theme options (light/dark) if available.
  - **Arguments:**
    - `queries` (array of strings, required): List of company or brand names to search for logos.
    - `format` (string, required): Output format ("JSX", "TSX", or "SVG").
  - **Usage:** Use when you need to add a company or brand logo to your project, or when a user types a command like `/logo GitHub`.
  - **Best Practices:**
    - Use specific brand/company names for best results.
    - Choose the output format that matches your codebase (e.g., TSX for TypeScript React projects).
    - Use for both single and batch logo requests.

- `21_magic_component_inspiration`:
  - **Purpose:** Fetch UI component inspiration and previews from 21st.dev's library, returning JSON data of matching components (no code generation).
  - **Arguments:**
    - `message` (string, required): Full user message describing the desired component or inspiration.
    - `searchQuery` (string, required): Two-to-four word search query for the component (e.g., "modern navbar").
  - **Usage:** Use when you want to see examples or get inspiration for UI components before generating or customizing code.
  - **Best Practices:**
    - Provide a clear, concise search query for relevant results.
    - Use to explore design options and gather ideas before building.

- `21st_magic_component_refiner`:
  - **Purpose:** Refine or improve an existing UI component based on user feedback or design requirements. Returns a redesigned version and implementation instructions.
  - **Arguments:**
    - `userMessage` (string, required): Full user message about the desired UI refinement.
    - `absolutePathToRefiningFile` (string, required): Absolute path to the file/component to refine.
    - `context` (string, required): Specific UI elements/aspects to improve, extracted from user message, code, and history.
  - **Usage:** Use when you want to enhance, restyle, or modernize an existing component (not for full pages).
  - **Best Practices:**
    - Focus on specific improvements (styling, layout, responsiveness) as described by the user.
    - Provide as much context as possible for accurate refinements.
    - Use for iterative design and rapid prototyping.

**General Best Practices:**
- Use for rapid prototyping, design sprints, or when you need high-quality UI components quickly.
- Customize generated components as needed to fit your project’s style and requirements.
- Break down complex UIs into smaller components for best results.
- Monitor your API usage and stay within free tier limits for larger projects.
- Review and adapt generated code to match your codebase’s conventions and architecture.
- Provide clear, descriptive prompts for more accurate and relevant component generation.
- Combine with other MCP servers (e.g., filesystem, memory) for end-to-end agentic workflows.

# Byterover MCP Server Usage Rules

**When to Use:**
- For retrieving, storing, and sharing coding solutions, insights, and best practices across projects and teams.
- When you need to access a curated knowledge base of past coding solutions to accelerate problem-solving.
- For automating the capture and reuse of technical knowledge, code snippets, and troubleshooting steps.
- When integrating AI-driven knowledge retrieval into agentic workflows or IDEs for developer productivity.

**Features:**
- Enables structured storage and retrieval of coding knowledge, solutions, and insights.
- Supports semantic and keyword-based search for relevant solutions.
- Facilitates sharing and collaboration on best practices within and across teams.
- Integrates with MCP-compatible clients and can be used in multi-agent or multi-step workflows.
- Provides APIs for programmatic access to knowledge, with support for updates and versioning (within free tier limits).
- Can be containerized or self-hosted for secure, private knowledge management.

**Available Tools:**

- `byterover-retrive-knowledge`:
  - **Purpose:** Retrieve relevant coding knowledge, solutions, and best practices from the shared ByteRover memory for use by AI agents or coding assistants.
  - **Arguments:**
    - `query` (string, required): Search terms to find relevant knowledge or context.
    - `limit` (integer, required): Maximum number of results to return (free tier: up to 500 retrievals per month).
  - **Usage:** Use before starting a new coding task to fetch prior solutions, code patterns, or project context.
  - **Best Practices:**
    - Always retrieve related context before beginning a new or complex task.
    - Use specific queries for targeted results; broad queries for exploration.
    - Stay within the free tier retrieval limit (500 per month as of July 2025).

- `byterover-store-knowledge`:
  - **Purpose:** Store new coding solutions, insights, or best practices into the shared ByteRover memory for future retrieval by yourself or your team.
  - **Arguments:**
    - `messages` (array of objects, required): Each object should include `role` ("user" or "assistant") and `content` (string) to capture the coding insight or solution.
  - **Usage:** Use after completing a task or solving a problem to save critical information for future use.
  - **Best Practices:**
    - Store all critical information and solutions after successful tasks.
    - Tag or describe entries clearly for easy future retrieval.
    - Use for both individual and team knowledge sharing.
    - There is no limit on storage in the free tier, but retrievals are capped.

**General Best Practices:**
- Use for tasks requiring rapid access to prior solutions, code patterns, or troubleshooting guides.
- Regularly update and curate the knowledge base to ensure relevance and accuracy.
- Tag and categorize knowledge entries for easier discovery and retrieval.
- Restrict access to trusted users and validate all inputs to prevent data leakage or injection.
- Document available tools, search strategies, and expected inputs/outputs for both agents and users.
- Monitor usage and set up alerts for unusual access or modification patterns.
- Combine with other MCP servers (e.g., filesystem, memory) for richer, context-aware developer workflows.
