"""Add new product fields to match UI

Revision ID: 001
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add new fields to products table
    op.add_column('products', sa.Column('order_quantity_minimum', sa.Integer(), nullable=True, server_default='1'))
    op.add_column('products', sa.Column('order_quantity_maximum', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('products', sa.Column('page_title', sa.String(), nullable=True))
    op.add_column('products', sa.Column('meta_description', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('preorder_release_date', sa.String(), nullable=True))
    op.add_column('products', sa.Column('preorder_message', sa.String(), nullable=True))
    op.add_column('products', sa.Column('is_preorder_only', sa.<PERSON>(), nullable=True, server_default='false'))
    op.add_column('products', sa.Column('is_price_hidden', sa.Boolean(), nullable=True, server_default='false'))
    op.add_column('products', sa.Column('price_hidden_label', sa.String(), nullable=True))
    op.add_column('products', sa.Column('open_graph_type', sa.String(), nullable=True, server_default='product'))
    op.add_column('products', sa.Column('open_graph_title', sa.String(), nullable=True))
    op.add_column('products', sa.Column('open_graph_description', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('open_graph_use_meta_description', sa.Boolean(), nullable=True, server_default='true'))
    op.add_column('products', sa.Column('open_graph_use_product_name', sa.Boolean(), nullable=True, server_default='true'))
    op.add_column('products', sa.Column('open_graph_use_image', sa.Boolean(), nullable=True, server_default='true'))
    op.add_column('products', sa.Column('gtin', sa.String(), nullable=True))
    op.add_column('products', sa.Column('mpn', sa.String(), nullable=True))
    op.add_column('products', sa.Column('status', sa.String(), nullable=True, server_default='active'))
    op.add_column('products', sa.Column('channel', sa.String(), nullable=True))
    op.add_column('products', sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    # Remove the new columns
    op.drop_column('products', 'last_sync_at')
    op.drop_column('products', 'channel')
    op.drop_column('products', 'status')
    op.drop_column('products', 'mpn')
    op.drop_column('products', 'gtin')
    op.drop_column('products', 'open_graph_use_image')
    op.drop_column('products', 'open_graph_use_product_name')
    op.drop_column('products', 'open_graph_use_meta_description')
    op.drop_column('products', 'open_graph_description')
    op.drop_column('products', 'open_graph_title')
    op.drop_column('products', 'open_graph_type')
    op.drop_column('products', 'price_hidden_label')
    op.drop_column('products', 'is_price_hidden')
    op.drop_column('products', 'is_preorder_only')
    op.drop_column('products', 'preorder_message')
    op.drop_column('products', 'preorder_release_date')
    op.drop_column('products', 'meta_description')
    op.drop_column('products', 'page_title')
    op.drop_column('products', 'order_quantity_maximum')
    op.drop_column('products', 'order_quantity_minimum') 