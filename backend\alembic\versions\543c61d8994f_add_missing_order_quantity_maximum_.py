"""Add missing order_quantity_maximum column

Revision ID: 543c61d8994f
Revises: 001
Create Date: 2025-07-17 11:46:24.678983

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '543c61d8994f'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('order_quantity_maximum', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_products_channel'), 'products', ['channel'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_products_channel'), table_name='products')
    op.drop_column('products', 'order_quantity_maximum')
    # ### end Alembic commands ###
