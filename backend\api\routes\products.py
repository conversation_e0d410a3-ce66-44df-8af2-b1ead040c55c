from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from typing import Optional
import uuid
from datetime import datetime
import math

from core.db import get_db
from models.product import (
    Product, 
    ProductCreate, 
    ProductUpdate, 
    ProductResponse, 
    ProductListResponse, 
    ProductListItem,
    ProductQueryParams
)

router = APIRouter()

# Helper function to generate product ID
def generate_product_id() -> str:
    return f"prod_{uuid.uuid4().hex[:12]}"

# Helper function to build query filters
def build_product_filters(db_query, params: ProductQueryParams):
    """Build SQLAlchemy query filters based on parameters"""
    
    if params.search:
        search_term = f"%{params.search.lower()}%"
        db_query = db_query.filter(
            or_(
                Product.name.ilike(search_term),
                Product.sku.ilike(search_term),
                Product.description.ilike(search_term)
            )
        )
    
    if params.is_visible is not None:
        db_query = db_query.filter(Product.is_visible == params.is_visible)
    
    if params.is_featured is not None:
        db_query = db_query.filter(Product.is_featured == params.is_featured)
    
    if params.sync_status:
        db_query = db_query.filter(Product.sync_status == params.sync_status)
    
    if params.category:
        # Filter by category (assuming categories are stored as JSON)
        db_query = db_query.filter(
            Product.categories.op('?')(params.category)
        )
    
    if params.brand:
        db_query = db_query.filter(Product.brand_name == params.brand)
    
    if params.channel:
        db_query = db_query.filter(Product.channel == params.channel)
    
    if params.price_min is not None:
        db_query = db_query.filter(Product.price >= params.price_min)
    
    if params.price_max is not None:
        db_query = db_query.filter(Product.price <= params.price_max)
    
    if params.inventory_min is not None:
        db_query = db_query.filter(Product.inventory_level >= params.inventory_min)
    
    if params.inventory_max is not None:
        db_query = db_query.filter(Product.inventory_level <= params.inventory_max)
    
    if params.date_from:
        try:
            from_date = datetime.fromisoformat(params.date_from.replace('Z', '+00:00'))
            db_query = db_query.filter(Product.created_at >= from_date)
        except ValueError:
            pass  # Skip invalid date format
    
    if params.date_to:
        try:
            to_date = datetime.fromisoformat(params.date_to.replace('Z', '+00:00'))
            db_query = db_query.filter(Product.created_at <= to_date)
        except ValueError:
            pass  # Skip invalid date format
    
    return db_query

# Helper function to apply sorting
def apply_sorting(db_query, sort_by: str, sort_order: str):
    """Apply sorting to the query"""
    
    # Map sort_by to actual column names
    sort_column_map = {
        "name": Product.name,
        "price": Product.price,
        "created_at": Product.created_at,
        "updated_at": Product.updated_at,
        "inventory_level": Product.inventory_level,
        "sync_status": Product.sync_status
    }
    
    sort_column = sort_column_map.get(sort_by, Product.created_at)
    
    if sort_order == "asc":
        return db_query.order_by(asc(sort_column))
    else:
        return db_query.order_by(desc(sort_column))

@router.get("/products", response_model=ProductListResponse)
async def get_products(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name, SKU, or description"),
    category: Optional[str] = Query(None, description="Filter by category"),
    brand: Optional[str] = Query(None, description="Filter by brand"),
    is_visible: Optional[bool] = Query(None, description="Filter by visibility"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    channel: Optional[str] = Query(None, description="Filter by channel"),
    price_min: Optional[float] = Query(None, description="Minimum price"),
    price_max: Optional[float] = Query(None, description="Maximum price"),
    inventory_min: Optional[int] = Query(None, description="Minimum inventory"),
    inventory_max: Optional[int] = Query(None, description="Maximum inventory"),
    date_from: Optional[str] = Query(None, description="Created from date"),
    date_to: Optional[str] = Query(None, description="Created to date"),
    sort_by: str = Query("created_at", regex="^(name|price|created_at|updated_at|inventory_level|sync_status)$", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    db: Session = Depends(get_db)
):
    """
    Get all products with filtering, pagination, and sorting
    
    Supports the following operations:
    - Pagination with page and limit
    - Search by name, SKU, or description
    - Filter by visibility, featured status, sync status, category, brand, channel
    - Filter by price range, inventory range, date range
    - Sort by name, price, created_at, updated_at, inventory_level, sync_status
    """
    
    # Create query parameters object
    params = ProductQueryParams(
        page=page,
        limit=limit,
        search=search,
        category=category,
        brand=brand,
        is_visible=is_visible,
        is_featured=is_featured,
        sync_status=sync_status,
        channel=channel,
        price_min=price_min,
        price_max=price_max,
        inventory_min=inventory_min,
        inventory_max=inventory_max,
        date_from=date_from,
        date_to=date_to,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    # Build base query
    db_query = db.query(Product)
    
    # Apply filters
    db_query = build_product_filters(db_query, params)
    
    # Get total count before pagination
    total = db_query.count()
    
    # Apply sorting
    db_query = apply_sorting(db_query, sort_by, sort_order)
    
    # Apply pagination
    offset = (page - 1) * limit
    products = db_query.offset(offset).limit(limit).all()
    
    # Calculate total pages
    total_pages = math.ceil(total / limit) if total > 0 else 1
    
    # Convert to list items with new fields
    product_items = []
    for product in products:
        # Get primary category name from categories JSON
        primary_category = None
        if product.categories and len(product.categories) > 0:
            primary_category = product.categories[0].get('name') if isinstance(product.categories[0], dict) else str(product.categories[0])
        
        product_items.append(ProductListItem(
            id=product.id,
            name=product.name,
            sku=product.sku,
            price=product.price,
            inventory_level=product.inventory_level,
            is_visible=product.is_visible,
            is_featured=product.is_featured,
            sync_status=product.sync_status,
            created_at=product.created_at,
            updated_at=product.updated_at,
            channel=product.channel,
            category=primary_category,
            brand=product.brand_name,
            last_sync_at=product.last_sync_at
        ))
    
    return ProductListResponse(
        products=product_items,
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages
    )

@router.get("/products/{product_id}", response_model=ProductResponse)
async def get_product(product_id: str, db: Session = Depends(get_db)):
    """
    Get a specific product by ID
    
    Returns detailed product information including all fields
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Increment view count
    product.view_count += 1
    db.commit()
    
    return ProductResponse.model_validate(product)

@router.post("/products", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(product_data: ProductCreate, db: Session = Depends(get_db)):
    """
    Create a new product
    
    Creates a new product with all the provided information
    """
    
    # Check if SKU already exists (if provided)
    if product_data.sku:
        existing_product = db.query(Product).filter(Product.sku == product_data.sku).first()
        if existing_product:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Product with SKU '{product_data.sku}' already exists"
            )
    
    # Generate unique product ID
    product_id = generate_product_id()
    
    # Create product instance
    db_product = Product(
        id=product_id,
        **product_data.dict()
    )
    
    # Add to database
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    
    return ProductResponse.model_validate(db_product)

@router.put("/products/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str, 
    product_data: ProductUpdate, 
    db: Session = Depends(get_db)
):
    """
    Update an existing product
    
    Updates product information with provided fields
    """
    
    # Get existing product
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Check SKU uniqueness if being updated
    if product_data.sku and product_data.sku != product.sku:
        existing_product = db.query(Product).filter(
            and_(Product.sku == product_data.sku, Product.id != product_id)
        ).first()
        if existing_product:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Product with SKU '{product_data.sku}' already exists"
            )
    
    # Update fields
    update_data = product_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(product, field, value)
    
    # Update timestamp
    product.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(product)
    
    return ProductResponse.model_validate(product)

@router.delete("/products/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(product_id: str, db: Session = Depends(get_db)):
    """
    Delete a product
    
    Permanently removes a product from the database
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    db.delete(product)
    db.commit()
    
    return None

@router.post("/products/{product_id}/sync")
async def sync_product(product_id: str, db: Session = Depends(get_db)):
    """
    Sync product with external platforms
    
    Simulates syncing product data with external e-commerce platforms
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Simulate sync process
    # In real implementation, this would sync with BigCommerce, Shopify, etc.
    product.sync_status = "synced"
    product.updated_at = datetime.utcnow()
    
    db.commit()
    
    return {
        "success": True,
        "message": f"Product {product_id} synced successfully",
        "sync_status": "synced",
        "timestamp": datetime.utcnow().isoformat()
    }
