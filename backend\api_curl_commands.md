# PIM API cURL Commands for Postman Testing

Base URL: `http://localhost:8000`

## 1. Health Check

```bash
curl -X GET "http://localhost:8000/api/health" \
  -H "Content-Type: application/json"
```

## 2. Get All Products (with filters)

```bash
# Basic get all products
curl -X GET "http://localhost:8000/api/products" \
  -H "Content-Type: application/json"

# With pagination
curl -X GET "http://localhost:8000/api/products?page=1&limit=10" \
  -H "Content-Type: application/json"

# With search
curl -X GET "http://localhost:8000/api/products?search=headphones" \
  -H "Content-Type: application/json"

# With filters
curl -X GET "http://localhost:8000/api/products?is_visible=true&is_featured=false&sync_status=synced" \
  -H "Content-Type: application/json"

# With price range
curl -X GET "http://localhost:8000/api/products?price_min=50&price_max=200" \
  -H "Content-Type: application/json"

# With inventory range
curl -X GET "http://localhost:8000/api/products?inventory_min=10&inventory_max=100" \
  -H "Content-Type: application/json"

# With sorting
curl -X GET "http://localhost:8000/api/products?sort_by=price&sort_order=desc" \
  -H "Content-Type: application/json"

# With channel filter
curl -X GET "http://localhost:8000/api/products?channel=bigcommerce" \
  -H "Content-Type: application/json"

# With brand filter
curl -X GET "http://localhost:8000/api/products?brand=AudioTech" \
  -H "Content-Type: application/json"

# Combined filters
curl -X GET "http://localhost:8000/api/products?search=headphones&is_visible=true&price_min=50&price_max=200&sort_by=price&sort_order=desc" \
  -H "Content-Type: application/json"
```

## 3. Get Single Product

```bash
# Replace {product_id} with actual product ID
curl -X GET "http://localhost:8000/api/products/{product_id}" \
  -H "Content-Type: application/json"
```

## 4. Create Product

```bash
curl -X POST "http://localhost:8000/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Wireless Bluetooth Headphones Pro",
    "product_type": "physical",
    "sku": "WBH-PRO-001",
    "description": "High-quality wireless headphones with noise cancellation",
    "price": 199.99,
    "cost_price": 120.00,
    "retail_price": 249.99,
    "sale_price": 0,
    "map_price": 0,
    "brand_name": "AudioTech",
    "inventory_level": 50,
    "inventory_warning_level": 10,
    "inventory_tracking": "simple",
    "weight": 0.5,
    "width": 8.0,
    "depth": 3.0,
    "height": 2.0,
    "is_visible": true,
    "is_featured": true,
    "is_free_shipping": false,
    "fixed_cost_shipping_price": 0,
    "warranty": "1 year manufacturer warranty",
    "bin_picking_number": "BIN-001",
    "upc": "123456789012",
    "search_keywords": "wireless, bluetooth, headphones, audio",
    "availability_description": "Usually ships in 24 hours",
    "availability": "available",
    "gift_wrapping_options_type": "any",
    "condition": "New",
    "is_condition_shown": true,
    "order_quantity_minimum": 1,
    "order_quantity_maximum": 0,
    "page_title": "Wireless Bluetooth Headphones Pro - AudioTech",
    "meta_description": "Premium wireless headphones with noise cancellation technology",
    "is_preorder_only": false,
    "is_price_hidden": false,
    "open_graph_type": "product",
    "open_graph_title": "Wireless Bluetooth Headphones Pro",
    "open_graph_description": "High-quality wireless headphones",
    "open_graph_use_meta_description": true,
    "open_graph_use_product_name": true,
    "open_graph_use_image": true,
    "gtin": "1234567890123",
    "mpn": "WBH-PRO-001",
    "status": "active",
    "channel": "bigcommerce",
    "categories": [
      {
        "id": "1",
        "name": "Electronics",
        "description": "Electronic devices and accessories"
      }
    ],
    "images": [
      {
        "image_url": "https://example.com/headphones-1.jpg",
        "is_thumbnail": true,
        "sort_order": 1,
        "description": "Main product image"
      }
    ],
    "videos": [],
    "variants": [],
    "custom_fields": [],
    "bulk_pricing_rules": []
  }'
```

## 5. Update Product

```bash
# Replace {product_id} with actual product ID
curl -X PUT "http://localhost:8000/api/products/{product_id}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Wireless Bluetooth Headphones Pro",
    "price": 189.99,
    "inventory_level": 45,
    "is_featured": true,
    "description": "Updated description with new features"
  }'
```

## 6. Delete Product

```bash
# Replace {product_id} with actual product ID
curl -X DELETE "http://localhost:8000/api/products/{product_id}" \
  -H "Content-Type: application/json"
```

## 7. Sync Product

```bash
# Replace {product_id} with actual product ID
curl -X POST "http://localhost:8000/api/products/{product_id}/sync" \
  -H "Content-Type: application/json"
```

## 8. Root Endpoint

```bash
curl -X GET "http://localhost:8000/" \
  -H "Content-Type: application/json"
```

## Sample Product Data for Testing

### Minimal Product (Required fields only)

```json
{
  "name": "Test Product",
  "price": 99.99
}
```

### Complete Product (All fields)

```json
{
  "name": "Complete Test Product",
  "product_type": "physical",
  "sku": "TEST-001",
  "description": "A comprehensive test product with all fields",
  "price": 199.99,
  "cost_price": 120.0,
  "retail_price": 249.99,
  "sale_price": 0,
  "map_price": 0,
  "tax_class_id": 1,
  "product_tax_code": "P0000000",
  "brand_id": 1,
  "brand_name": "TestBrand",
  "inventory_level": 100,
  "inventory_warning_level": 10,
  "inventory_tracking": "simple",
  "fixed_cost_shipping_price": 0,
  "is_free_shipping": false,
  "is_visible": true,
  "is_featured": false,
  "warranty": "1 year warranty",
  "bin_picking_number": "BIN-TEST-001",
  "layout_file": "",
  "upc": "123456789012",
  "search_keywords": "test, product, sample",
  "availability_description": "In stock",
  "availability": "available",
  "gift_wrapping_options_type": "any",
  "sort_order": 0,
  "condition": "New",
  "is_condition_shown": true,
  "order_quantity_minimum": 1,
  "order_quantity_maximum": 0,
  "page_title": "Test Product Page Title",
  "meta_description": "Test product meta description for SEO",
  "preorder_release_date": "",
  "preorder_message": "",
  "is_preorder_only": false,
  "is_price_hidden": false,
  "price_hidden_label": "",
  "open_graph_type": "product",
  "open_graph_title": "Test Product OG Title",
  "open_graph_description": "Test product OG description",
  "open_graph_use_meta_description": true,
  "open_graph_use_product_name": true,
  "open_graph_use_image": true,
  "gtin": "1234567890123",
  "mpn": "TEST-001",
  "status": "active",
  "channel": "bigcommerce",
  "categories": [
    {
      "id": "1",
      "name": "Test Category",
      "description": "Test category description"
    }
  ],
  "images": [
    {
      "image_url": "https://example.com/test-image.jpg",
      "is_thumbnail": true,
      "sort_order": 1,
      "description": "Test product image"
    }
  ],
  "videos": [],
  "variants": [],
  "custom_fields": [
    {
      "name": "Test Custom Field",
      "value": "Test Value"
    }
  ],
  "bulk_pricing_rules": [
    {
      "quantity_min": 10,
      "quantity_max": 49,
      "rule_type": "fixed",
      "amount": 189.99
    }
  ]
}
```

## Testing Tips

1. **Start with Health Check** - Verify the API is running
2. **Create a product first** - Use the POST endpoint to create test data
3. **Get the product ID** - From the create response, use that ID for other operations
4. **Test filters** - Try different combinations of query parameters
5. **Test validation** - Try sending invalid data to test error responses

## Common Error Responses

- **400 Bad Request** - Invalid data or missing required fields
- **404 Not Found** - Product ID doesn't exist
- **422 Validation Error** - Invalid field values or types
- **500 Internal Server Error** - Server-side issues

## Postman Collection Import

You can import these as a Postman collection by:

1. Creating a new collection in Postman
2. Adding requests for each endpoint
3. Using the cURL commands as reference for the request structure
