#!/usr/bin/env python3
"""
Check what columns actually exist in the products table
"""

from core.db import engine
import sqlalchemy

def check_columns():
    print("🔍 Checking products table columns...")
    
    try:
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'products' 
                ORDER BY ordinal_position;
            """))
            
            columns = result.fetchall()
            
            print(f"Found {len(columns)} columns in products table:")
            print("-" * 60)
            
            missing_columns = []
            expected_new_columns = [
                'order_quantity_minimum', 'order_quantity_maximum', 'page_title', 
                'meta_description', 'preorder_release_date', 'preorder_message',
                'is_preorder_only', 'is_price_hidden', 'price_hidden_label',
                'open_graph_type', 'open_graph_title', 'open_graph_description',
                'open_graph_use_meta_description', 'open_graph_use_product_name',
                'open_graph_use_image', 'gtin', 'mpn', 'status', 'channel', 'last_sync_at'
            ]
            
            existing_columns = [col[0] for col in columns]
            
            for col in columns:
                column_name, data_type, is_nullable, default = col
                print(f"{column_name:<30} {data_type:<15} {is_nullable:<10} {default or 'NULL'}")
            
            print("\n" + "=" * 60)
            print("Checking for missing expected columns:")
            
            for expected_col in expected_new_columns:
                if expected_col not in existing_columns:
                    missing_columns.append(expected_col)
                    print(f"❌ Missing: {expected_col}")
                else:
                    print(f"✅ Found: {expected_col}")
            
            if missing_columns:
                print(f"\n⚠️  {len(missing_columns)} columns are missing!")
                return False
            else:
                print(f"\n✅ All expected columns are present!")
                return True
                
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        return False

if __name__ == "__main__":
    check_columns()
