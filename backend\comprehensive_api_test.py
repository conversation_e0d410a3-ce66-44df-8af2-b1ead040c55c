#!/usr/bin/env python3
"""
Comprehensive API test suite for PIM API
Tests all endpoints with multiple product entries
"""

import requests
import json
import sys
from datetime import datetime
from typing import List, Dict, Any

BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self):
        self.created_products = []
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }

    def log_result(self, test_name: str, success: bool, message: str = ""):
        if success:
            print(f"✅ {test_name}")
            self.test_results["passed"] += 1
        else:
            print(f"❌ {test_name}: {message}")
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {message}")

    def test_health_check(self):
        """Test health endpoint"""
        print("\n🔍 Testing Health Check...")
        try:
            response = requests.get(f"{BASE_URL}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_result("Health Check", True, f"Status: {data['status']}")
                return True
            else:
                self.log_result("Health Check", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Health Check", False, str(e))
            return False

    def create_test_products(self):
        """Create 3 test products"""
        print("\n📦 Creating Test Products...")
        
        test_products = [
            {
                "name": "Premium Laptop",
                "product_type": "physical",
                "sku": f"LAPTOP-{datetime.now().strftime('%Y%m%d%H%M%S')}-001",
                "description": "High-performance laptop for professionals",
                "price": 1299.99,
                "cost_price": 800.00,
                "retail_price": 1399.99,
                "inventory_level": 50,
                "weight": 2.5,
                "width": 35.0,
                "depth": 25.0,
                "height": 2.0,
                "brand_name": "TechBrand",
                "is_visible": True,
                "is_featured": True,
                "categories": [{"id": "1", "name": "Electronics"}, {"id": "2", "name": "Computers"}],
                "custom_fields": [{"name": "warranty", "value": "2 years"}]
            },
            {
                "name": "Wireless Mouse",
                "product_type": "physical", 
                "sku": f"MOUSE-{datetime.now().strftime('%Y%m%d%H%M%S')}-002",
                "description": "Ergonomic wireless mouse with precision tracking",
                "price": 49.99,
                "cost_price": 20.00,
                "inventory_level": 200,
                "weight": 0.1,
                "brand_name": "AccessoryBrand",
                "is_visible": True,
                "is_featured": False,
                "categories": [{"id": "1", "name": "Electronics"}, {"id": "3", "name": "Accessories"}]
            },
            {
                "name": "Software License",
                "product_type": "digital",
                "sku": f"SOFTWARE-{datetime.now().strftime('%Y%m%d%H%M%S')}-003", 
                "description": "Professional software license for productivity",
                "price": 199.99,
                "cost_price": 50.00,
                "inventory_level": 1000,
                "is_visible": True,
                "is_featured": True,
                "categories": [{"id": "4", "name": "Software"}]
            }
        ]

        for i, product_data in enumerate(test_products, 1):
            try:
                response = requests.post(f"{BASE_URL}/api/products", json=product_data, timeout=10)
                if response.status_code == 201:
                    product = response.json()
                    self.created_products.append(product)
                    self.log_result(f"Create Product {i} ({product_data['name']})", True, f"ID: {product['id']}")
                else:
                    self.log_result(f"Create Product {i}", False, f"Status: {response.status_code}, Response: {response.text}")
            except Exception as e:
                self.log_result(f"Create Product {i}", False, str(e))

        return len(self.created_products) > 0

    def test_get_all_products(self):
        """Test getting all products with pagination"""
        print("\n📋 Testing Get All Products...")
        
        try:
            # Test default pagination
            response = requests.get(f"{BASE_URL}/api/products", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("Get All Products (Default)", True, 
                              f"Total: {data['total']}, Page: {data['page']}, Products: {len(data['products'])}")
                
                # Test with specific pagination
                response = requests.get(f"{BASE_URL}/api/products?page=1&limit=5", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    self.log_result("Get All Products (Paginated)", True,
                                  f"Limit: {data['limit']}, Products returned: {len(data['products'])}")
                else:
                    self.log_result("Get All Products (Paginated)", False, f"Status: {response.status_code}")
            else:
                self.log_result("Get All Products", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("Get All Products", False, str(e))

    def test_search_and_filter(self):
        """Test search and filtering functionality"""
        print("\n🔍 Testing Search and Filtering...")
        
        if not self.created_products:
            self.log_result("Search Tests", False, "No products created to search")
            return

        # Test search by name
        try:
            response = requests.get(f"{BASE_URL}/api/products?search=laptop", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("Search by Name", True, f"Found {len(data['products'])} products")
            else:
                self.log_result("Search by Name", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("Search by Name", False, str(e))

        # Test filter by visibility
        try:
            response = requests.get(f"{BASE_URL}/api/products?is_visible=true", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("Filter by Visibility", True, f"Found {len(data['products'])} visible products")
            else:
                self.log_result("Filter by Visibility", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("Filter by Visibility", False, str(e))

        # Test filter by featured
        try:
            response = requests.get(f"{BASE_URL}/api/products?is_featured=true", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("Filter by Featured", True, f"Found {len(data['products'])} featured products")
            else:
                self.log_result("Filter by Featured", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("Filter by Featured", False, str(e))

    def test_get_individual_products(self):
        """Test getting individual product details"""
        print("\n🔍 Testing Individual Product Details...")
        
        for i, product in enumerate(self.created_products, 1):
            try:
                response = requests.get(f"{BASE_URL}/api/products/{product['id']}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    self.log_result(f"Get Product {i} Details", True, 
                                  f"Name: {data['name']}, View Count: {data['view_count']}")
                else:
                    self.log_result(f"Get Product {i} Details", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_result(f"Get Product {i} Details", False, str(e))

    def test_update_products(self):
        """Test updating products"""
        print("\n✏️ Testing Product Updates...")
        
        for i, product in enumerate(self.created_products, 1):
            update_data = {
                "price": product["price"] + 10.00,
                "description": f"Updated description for {product['name']}",
                "is_featured": not product.get("is_featured", False)
            }
            
            try:
                response = requests.put(f"{BASE_URL}/api/products/{product['id']}", 
                                      json=update_data, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    self.log_result(f"Update Product {i}", True, 
                                  f"New Price: ${data['price']}, Featured: {data['is_featured']}")
                else:
                    self.log_result(f"Update Product {i}", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_result(f"Update Product {i}", False, str(e))

    def test_delete_products(self):
        """Test deleting products (optional - only delete one to test functionality)"""
        print("\n🗑️ Testing Product Deletion...")
        
        if self.created_products:
            # Only delete the last product to test delete functionality
            product_to_delete = self.created_products[-1]
            try:
                response = requests.delete(f"{BASE_URL}/api/products/{product_to_delete['id']}", timeout=10)
                if response.status_code == 204:
                    self.log_result("Delete Product", True, f"Deleted: {product_to_delete['name']}")
                    
                    # Verify deletion
                    response = requests.get(f"{BASE_URL}/api/products/{product_to_delete['id']}", timeout=10)
                    if response.status_code == 404:
                        self.log_result("Verify Deletion", True, "Product not found (correctly deleted)")
                    else:
                        self.log_result("Verify Deletion", False, "Product still exists after deletion")
                else:
                    self.log_result("Delete Product", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("Delete Product", False, str(e))

    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Comprehensive PIM API Tests...")
        print("=" * 60)
        
        # Test health first
        if not self.test_health_check():
            print("\n❌ API is not healthy. Stopping tests.")
            return False
        
        # Create test products
        if not self.create_test_products():
            print("\n❌ Failed to create test products. Stopping tests.")
            return False
        
        # Run all other tests
        self.test_get_all_products()
        self.test_search_and_filter()
        self.test_get_individual_products()
        self.test_update_products()
        self.test_delete_products()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        print(f"📈 Success Rate: {(self.test_results['passed'] / (self.test_results['passed'] + self.test_results['failed']) * 100):.1f}%")
        
        if self.test_results['errors']:
            print(f"\n❌ Errors:")
            for error in self.test_results['errors']:
                print(f"   • {error}")
        
        print(f"\n📦 Created Products: {len(self.created_products)}")
        for product in self.created_products:
            print(f"   • {product['name']} (ID: {product['id']})")
        
        return self.test_results['failed'] == 0

if __name__ == "__main__":
    tester = APITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
