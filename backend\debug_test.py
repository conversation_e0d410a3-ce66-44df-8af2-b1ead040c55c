#!/usr/bin/env python3
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def debug_test():
    print("🔍 Debug API Test")
    print("=" * 30)
    
    # Test health
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        print(f"Health: {response.status_code}")
        if response.status_code == 200:
            print(f"✅ Health OK: {response.json()}")
        else:
            print(f"❌ Health failed: {response.text}")
            return
    except Exception as e:
        print(f"❌ Health error: {e}")
        return
    
    # Test simple product creation with detailed error handling
    product_data = {
        "name": "Debug Test Product",
        "price": 9.99
    }
    
    print(f"\n📦 Testing product creation...")
    print(f"Data: {json.dumps(product_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/products", 
            json=product_data, 
            timeout=10,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Success! Product created:")
            print(json.dumps(data, indent=2, default=str))
        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response text: {response.text}")
            try:
                error_data = response.json()
                print(f"Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error as JSON")
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is server running?")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    debug_test()
