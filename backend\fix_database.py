#!/usr/bin/env python3
"""
Script to add missing columns to the products table
"""
import sqlite3
import os

def add_missing_columns():
    """Add missing columns to the products table"""
    
    # Check if SQLite database exists
    db_path = "pim_db.sqlite"
    if not os.path.exists(db_path):
        print(f"Database {db_path} not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get current table schema
    cursor.execute("PRAGMA table_info(products)")
    columns = [column[1] for column in cursor.fetchall()]
    print(f"Current columns: {columns}")
    
    # Define missing columns to add
    missing_columns = [
        ("order_quantity_minimum", "INTEGER DEFAULT 1"),
        ("order_quantity_maximum", "INTEGER DEFAULT 0"),
        ("page_title", "TEXT"),
        ("meta_description", "TEXT"),
        ("preorder_release_date", "TEXT"),
        ("preorder_message", "TEXT"),
        ("is_preorder_only", "BOOLEAN DEFAULT FALSE"),
        ("is_price_hidden", "BOOLEAN DEFAULT FALSE"),
        ("price_hidden_label", "TEXT"),
        ("open_graph_type", "TEXT"),
        ("open_graph_title", "TEXT"),
        ("open_graph_description", "TEXT"),
        ("open_graph_use_meta_description", "BOOLEAN DEFAULT TRUE"),
        ("open_graph_use_product_name", "BOOLEAN DEFAULT TRUE"),
        ("open_graph_use_image", "BOOLEAN DEFAULT TRUE"),
        ("gtin", "TEXT"),
        ("mpn", "TEXT"),
        ("status", "TEXT DEFAULT 'active'"),
        ("channel", "TEXT"),
        ("last_sync_at", "TEXT"),
        ("view_count", "INTEGER DEFAULT 0"),
        ("total_sold", "INTEGER DEFAULT 0"),
        ("categories", "TEXT"),
        ("images", "TEXT"),
        ("videos", "TEXT"),
        ("variants", "TEXT"),
        ("custom_fields", "TEXT"),
        ("bulk_pricing_rules", "TEXT"),
        ("brand_name", "TEXT"),
        ("inventory_warning_level", "INTEGER DEFAULT 0"),
        ("inventory_tracking", "TEXT DEFAULT 'product'"),
        ("fixed_cost_shipping_price", "REAL DEFAULT 0.0"),
        ("is_free_shipping", "BOOLEAN DEFAULT FALSE"),
        ("warranty", "TEXT"),
        ("bin_picking_number", "TEXT"),
        ("layout_file", "TEXT"),
        ("upc", "TEXT"),
        ("search_keywords", "TEXT"),
        ("availability_description", "TEXT"),
        ("availability", "TEXT DEFAULT 'available'"),
        ("gift_wrapping_options_type", "TEXT"),
        ("sort_order", "INTEGER DEFAULT 0"),
        ("condition", "TEXT DEFAULT 'new'"),
        ("is_condition_shown", "BOOLEAN DEFAULT FALSE")
    ]
    
    # Add missing columns
    for column_name, column_type in missing_columns:
        if column_name not in columns:
            try:
                cursor.execute(f"ALTER TABLE products ADD COLUMN {column_name} {column_type}")
                print(f"✅ Added column: {column_name}")
            except sqlite3.OperationalError as e:
                print(f"❌ Error adding {column_name}: {e}")
        else:
            print(f"⏭️  Column already exists: {column_name}")
    
    # Commit changes
    conn.commit()
    conn.close()
    print("\n✅ Database schema updated successfully!")

if __name__ == "__main__":
    add_missing_columns() 