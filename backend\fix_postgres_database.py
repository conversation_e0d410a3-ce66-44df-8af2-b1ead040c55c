#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix PostgreSQL database schema by adding missing columns
"""
import psycopg2
import os
from psycopg2.extras import RealDictCursor

def fix_postgres_schema():
    """Add missing columns to PostgreSQL products table"""
    
    # Database connection parameters
    db_params = {
        'host': 'localhost',
        'port': 5432,
        'database': 'pim_db',
        'user': 'postgres',
        'password': 'password'
    }
    
    try:
        # Connect to PostgreSQL
        print("🔌 Connecting to PostgreSQL database...")
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        print("✅ Connected to PostgreSQL successfully!")
        
        # SQL commands to add missing columns
        sql_commands = [
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS order_quantity_minimum INTEGER DEFAULT 1",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS order_quantity_maximum INTEGER DEFAULT 0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS page_title TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS meta_description TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS preorder_release_date TIMESTAMP",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS preorder_message TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS is_preorder_only BOOLEAN DEFAULT FALSE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS is_price_hidden BOOLEAN DEFAULT FALSE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS price_hidden_label TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_type TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_title TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_description TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_meta_description BOOLEAN DEFAULT TRUE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_product_name BOOLEAN DEFAULT TRUE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_image BOOLEAN DEFAULT TRUE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS gtin TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS mpn TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active'",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS channel TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMP",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS total_sold INTEGER DEFAULT 0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS categories JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS images JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS videos JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS variants JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS custom_fields JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS bulk_pricing_rules JSONB",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS brand_name TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_warning_level INTEGER DEFAULT 0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_tracking TEXT DEFAULT 'product'",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS fixed_cost_shipping_price DECIMAL(10,2) DEFAULT 0.0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS is_free_shipping BOOLEAN DEFAULT FALSE",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS warranty TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS bin_picking_number TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS layout_file TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS upc TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS search_keywords TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS availability_description TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS availability TEXT DEFAULT 'available'",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS gift_wrapping_options_type TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS condition TEXT DEFAULT 'new'",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS is_condition_shown BOOLEAN DEFAULT FALSE"
        ]
        
        # Execute each SQL command
        print("🔧 Adding missing columns to products table...")
        for i, sql in enumerate(sql_commands, 1):
            try:
                cursor.execute(sql)
                print(f"✅ Added column {i}/{len(sql_commands)}")
            except Exception as e:
                print(f"⚠️  Column might already exist: {e}")
        
        # Commit changes
        conn.commit()
        print("✅ All columns added successfully!")
        
        # Verify the columns were added
        print("\n📋 Verifying table schema...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'products' 
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print(f"📊 Total columns in products table: {len(columns)}")
        
        # Check for specific missing columns
        column_names = [col[0] for col in columns]
        required_columns = [
            'order_quantity_minimum', 'order_quantity_maximum', 'page_title',
            'meta_description', 'channel', 'brand_name', 'last_sync_at'
        ]
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Still missing columns: {missing_columns}")
        else:
            print("✅ All required columns are present!")
        
        cursor.close()
        conn.close()
        print("✅ Database schema updated successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure PostgreSQL is running and the connection parameters are correct.")

if __name__ == "__main__":
    fix_postgres_schema() 