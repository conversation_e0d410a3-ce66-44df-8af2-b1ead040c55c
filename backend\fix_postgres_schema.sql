-- SQL script to add missing columns to PostgreSQL products table
-- Run this script in your PostgreSQL database

-- Add missing columns to products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS order_quantity_minimum INTEGER DEFAULT 1;
ALTER TABLE products ADD COLUMN IF NOT EXISTS order_quantity_maximum INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS page_title TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS meta_description TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS preorder_release_date TIMESTAMP;
ALTER TABLE products ADD COLUMN IF NOT EXISTS preorder_message TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_preorder_only BOOLEAN DEFAULT FALSE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_price_hidden BOOLEAN DEFAULT FALSE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS price_hidden_label TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_type TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_title TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_description TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_meta_description BOOLEAN DEFAULT TRUE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_product_name BOOLEAN DEFAULT TRUE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS open_graph_use_image BOOLEAN DEFAULT TRUE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS gtin TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS mpn TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';
ALTER TABLE products ADD COLUMN IF NOT EXISTS channel TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMP;
ALTER TABLE products ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS total_sold INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS categories JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS images JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS videos JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS variants JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS custom_fields JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS bulk_pricing_rules JSONB;
ALTER TABLE products ADD COLUMN IF NOT EXISTS brand_name TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_warning_level INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_tracking TEXT DEFAULT 'product';
ALTER TABLE products ADD COLUMN IF NOT EXISTS fixed_cost_shipping_price DECIMAL(10,2) DEFAULT 0.0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_free_shipping BOOLEAN DEFAULT FALSE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS warranty TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS bin_picking_number TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS layout_file TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS upc TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS search_keywords TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS availability_description TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS availability TEXT DEFAULT 'available';
ALTER TABLE products ADD COLUMN IF NOT EXISTS gift_wrapping_options_type TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS condition TEXT DEFAULT 'new';
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_condition_shown BOOLEAN DEFAULT FALSE;

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position; 