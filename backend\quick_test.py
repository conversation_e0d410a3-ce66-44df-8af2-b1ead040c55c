#!/usr/bin/env python3
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def quick_test():
    print("🚀 Quick API Test")
    print("=" * 30)
    
    # Test health
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        print(f"Health: {response.status_code} - {response.json()['status']}")
    except Exception as e:
        print(f"Health failed: {e}")
        return
    
    # Test simple product creation
    product_data = {
        "name": "Quick Test Product",
        "price": 9.99,
        "sku": f"QUICK-{datetime.now().strftime('%H%M%S')}"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/products", json=product_data, timeout=10)
        print(f"Create Product: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Success! Product ID: {data['id']}")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"Create failed: {e}")

if __name__ == "__main__":
    quick_test()
