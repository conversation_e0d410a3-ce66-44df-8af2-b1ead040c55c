"""
Custom exceptions for the application.
"""
from fastapi import HTTPException, status


class ProductNotFoundException(HTTPException):
    """Raised when a product is not found."""
    
    def __init__(self, product_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )


class ProductAlreadyExistsException(HTTPException):
    """Raised when trying to create a product that already exists."""
    
    def __init__(self, sku: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Product with SKU {sku} already exists"
        )


class DatabaseException(HTTPException):
    """Raised when there's a database error."""
    
    def __init__(self, detail: str = "Database error occurred"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class ValidationException(HTTPException):
    """Raised when there's a validation error."""
    
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail
        ) 