"""
Product models for the PIM module.
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB
from src.database import Base
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

# SQLAlchemy Model
class Product(Base):
    __tablename__ = "products"

    # Primary fields
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    product_type = Column(String, default="physical")  # physical, digital
    sku = Column(String, unique=True, index=True)
    description = Column(Text)
    
    # Dimensions and weight
    weight = Column(Float, default=0.0)
    width = Column(Float, default=0.0)
    depth = Column(Float, default=0.0)
    height = Column(Float, default=0.0)
    
    # Pricing
    price = Column(Float, nullable=False)
    cost_price = Column(Float, default=0.0)
    retail_price = Column(Float, default=0.0)
    sale_price = Column(Float, default=0.0)
    map_price = Column(Float, default=0.0)
    
    # Tax information
    tax_class_id = Column(Integer, default=0)
    product_tax_code = Column(String)
    
    # Brand information
    brand_id = Column(Integer, default=0)
    brand_name = Column(String)
    
    # Inventory
    inventory_level = Column(Integer, default=0)
    inventory_warning_level = Column(Integer, default=0)
    inventory_tracking = Column(String, default="none")  # none, simple, variant
    
    # Shipping
    fixed_cost_shipping_price = Column(Float, default=0.0)
    is_free_shipping = Column(Boolean, default=False)
    
    # Visibility and features
    is_visible = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Additional fields
    warranty = Column(String)
    bin_picking_number = Column(String)
    layout_file = Column(String)
    upc = Column(String)
    search_keywords = Column(String)
    availability_description = Column(String)
    availability = Column(String, default="available")
    gift_wrapping_options_type = Column(String, default="any")
    sort_order = Column(Integer, default=0)
    condition = Column(String, default="New")
    is_condition_shown = Column(Boolean, default=True)
    
    # New fields to match UI
    order_quantity_minimum = Column(Integer, default=1)
    order_quantity_maximum = Column(Integer, default=0)
    page_title = Column(String)
    meta_description = Column(Text)
    preorder_release_date = Column(String)
    preorder_message = Column(String)
    is_preorder_only = Column(Boolean, default=False)
    is_price_hidden = Column(Boolean, default=False)
    price_hidden_label = Column(String)
    open_graph_type = Column(String, default="product")
    open_graph_title = Column(String)
    open_graph_description = Column(Text)
    open_graph_use_meta_description = Column(Boolean, default=True)
    open_graph_use_product_name = Column(Boolean, default=True)
    open_graph_use_image = Column(Boolean, default=True)
    gtin = Column(String)
    mpn = Column(String)
    status = Column(String, default="active")  # active, inactive, draft
    
    # Channel and sync information
    channel = Column(String, index=True)  # bigcommerce, shopify, amazon, etc.
    last_sync_at = Column(DateTime(timezone=True))
    
    # Metadata
    view_count = Column(Integer, default=0)
    total_sold = Column(Integer, default=0)
    sync_status = Column(String, default="not_synced")  # not_synced, synced, error
    
    # JSON fields for complex data
    categories = Column(JSONB, default=list)
    images = Column(JSONB, default=list)
    videos = Column(JSONB, default=list)
    variants = Column(JSONB, default=list)
    custom_fields = Column(JSONB, default=list)
    bulk_pricing_rules = Column(JSONB, default=list)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

# Pydantic Schemas

class ProductTypeEnum(str, Enum):
    physical = "physical"
    digital = "digital"

class InventoryTrackingEnum(str, Enum):
    none = "none"
    simple = "simple"
    variant = "variant"

class AvailabilityEnum(str, Enum):
    available = "available"
    disabled = "disabled"
    preorder = "preorder"

class SyncStatusEnum(str, Enum):
    not_synced = "not_synced"
    synced = "synced"
    error = "error"

class ProductBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    product_type: ProductTypeEnum = ProductTypeEnum.physical
    sku: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    weight: Optional[float] = Field(0.0, ge=0)
    width: Optional[float] = Field(0.0, ge=0)
    depth: Optional[float] = Field(0.0, ge=0)
    height: Optional[float] = Field(0.0, ge=0)
    price: float = Field(..., gt=0)
    cost_price: Optional[float] = Field(0.0, ge=0)
    retail_price: Optional[float] = Field(0.0, ge=0)
    sale_price: Optional[float] = Field(0.0, ge=0)
    map_price: Optional[float] = Field(0.0, ge=0)
    tax_class_id: Optional[int] = Field(0, ge=0)
    product_tax_code: Optional[str] = None
    brand_id: Optional[int] = Field(0, ge=0)
    brand_name: Optional[str] = None
    inventory_level: int = Field(0, ge=0)
    inventory_warning_level: Optional[int] = Field(0, ge=0)
    inventory_tracking: InventoryTrackingEnum = InventoryTrackingEnum.none
    fixed_cost_shipping_price: Optional[float] = Field(0.0, ge=0)
    is_free_shipping: bool = False
    is_visible: bool = True
    is_featured: bool = False
    warranty: Optional[str] = None
    bin_picking_number: Optional[str] = None
    layout_file: Optional[str] = None
    upc: Optional[str] = None
    search_keywords: Optional[str] = None
    availability_description: Optional[str] = None
    availability: AvailabilityEnum = AvailabilityEnum.available
    gift_wrapping_options_type: str = "any"
    sort_order: Optional[int] = Field(0, ge=0)
    condition: str = "New"
    is_condition_shown: bool = True
    
    # New fields to match UI
    order_quantity_minimum: Optional[int] = Field(1, ge=1)
    order_quantity_maximum: Optional[int] = Field(None, ge=0)
    page_title: Optional[str] = None
    meta_description: Optional[str] = None
    preorder_release_date: Optional[str] = None
    preorder_message: Optional[str] = None
    is_preorder_only: bool = False
    is_price_hidden: bool = False
    price_hidden_label: Optional[str] = None
    open_graph_type: str = "product"
    open_graph_title: Optional[str] = None
    open_graph_description: Optional[str] = None
    open_graph_use_meta_description: bool = True
    open_graph_use_product_name: bool = True
    open_graph_use_image: bool = True
    gtin: Optional[str] = None
    mpn: Optional[str] = None
    status: str = "active"  # active, inactive, draft
    
    # Channel and sync information
    channel: Optional[str] = None
    last_sync_at: Optional[datetime] = None
    
    categories: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    images: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    videos: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    variants: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    custom_fields: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    bulk_pricing_rules: Optional[List[Dict[str, Any]]] = Field(default_factory=list)

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    product_type: Optional[ProductTypeEnum] = None
    sku: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    weight: Optional[float] = Field(None, ge=0)
    width: Optional[float] = Field(None, ge=0)
    depth: Optional[float] = Field(None, ge=0)
    height: Optional[float] = Field(None, ge=0)
    price: Optional[float] = Field(None, gt=0)
    cost_price: Optional[float] = Field(None, ge=0)
    retail_price: Optional[float] = Field(None, ge=0)
    sale_price: Optional[float] = Field(None, ge=0)
    map_price: Optional[float] = Field(None, ge=0)
    tax_class_id: Optional[int] = Field(None, ge=0)
    product_tax_code: Optional[str] = None
    brand_id: Optional[int] = Field(None, ge=0)
    brand_name: Optional[str] = None
    inventory_level: Optional[int] = Field(None, ge=0)
    inventory_warning_level: Optional[int] = Field(None, ge=0)
    inventory_tracking: Optional[InventoryTrackingEnum] = None
    fixed_cost_shipping_price: Optional[float] = Field(None, ge=0)
    is_free_shipping: Optional[bool] = None
    is_visible: Optional[bool] = None
    is_featured: Optional[bool] = None
    warranty: Optional[str] = None
    bin_picking_number: Optional[str] = None
    layout_file: Optional[str] = None
    upc: Optional[str] = None
    search_keywords: Optional[str] = None
    availability_description: Optional[str] = None
    availability: Optional[AvailabilityEnum] = None
    gift_wrapping_options_type: Optional[str] = None
    sort_order: Optional[int] = Field(None, ge=0)
    condition: Optional[str] = None
    is_condition_shown: Optional[bool] = None
    
    # New fields to match UI
    order_quantity_minimum: Optional[int] = Field(None, ge=1)
    order_quantity_maximum: Optional[int] = Field(None, ge=0)
    page_title: Optional[str] = None
    meta_description: Optional[str] = None
    preorder_release_date: Optional[str] = None
    preorder_message: Optional[str] = None
    is_preorder_only: Optional[bool] = None
    is_price_hidden: Optional[bool] = None
    price_hidden_label: Optional[str] = None
    open_graph_type: Optional[str] = None
    open_graph_title: Optional[str] = None
    open_graph_description: Optional[str] = None
    open_graph_use_meta_description: Optional[bool] = None
    open_graph_use_product_name: Optional[bool] = None
    open_graph_use_image: Optional[bool] = None
    gtin: Optional[str] = None
    mpn: Optional[str] = None
    status: Optional[str] = None  # active, inactive, draft
    
    # Channel and sync information
    channel: Optional[str] = None
    last_sync_at: Optional[datetime] = None
    
    categories: Optional[List[Dict[str, Any]]] = None
    images: Optional[List[Dict[str, Any]]] = None
    videos: Optional[List[Dict[str, Any]]] = None
    variants: Optional[List[Dict[str, Any]]] = None
    custom_fields: Optional[List[Dict[str, Any]]] = None
    bulk_pricing_rules: Optional[List[Dict[str, Any]]] = None

class ProductResponse(ProductBase):
    id: str
    view_count: int
    total_sold: int
    sync_status: SyncStatusEnum
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ProductListItem(BaseModel):
    id: str
    name: str
    sku: Optional[str]
    price: float
    inventory_level: int
    is_visible: bool
    is_featured: bool
    sync_status: SyncStatusEnum
    created_at: datetime
    updated_at: datetime
    
    # New fields to match UI
    channel: Optional[str] = None
    category: Optional[str] = None  # Primary category name
    brand: Optional[str] = None
    last_sync_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProductQueryParams(BaseModel):
    page: int = Field(1, ge=1)
    limit: int = Field(20, ge=1, le=100)
    search: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    is_visible: Optional[bool] = None
    is_featured: Optional[bool] = None
    sync_status: Optional[SyncStatusEnum] = None
    channel: Optional[str] = None
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    inventory_min: Optional[int] = None
    inventory_max: Optional[int] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    sort_by: Optional[str] = Field("created_at", pattern="^(name|price|created_at|updated_at|inventory_level|sync_status)$")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$")

class ProductListResponse(BaseModel):
    products: List[ProductListItem]
    total: int
    page: int
    limit: int
    total_pages: int 