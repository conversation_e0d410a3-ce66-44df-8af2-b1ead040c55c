"""
API routes for the PIM module.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional

from src.database import get_db
from src.pim.schemas import (
    ProductCreate, ProductUpdate, ProductResponse, ProductListResponse,
    ProductQueryParams, SyncStatusEnum
)
from src.pim.service import ProductService
from src.exceptions import ProductNotFoundException, ProductAlreadyExistsException

router = APIRouter(prefix="/api", tags=["products"])


@router.get("/products", response_model=ProductListResponse)
async def get_products(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name, SKU, or description"),
    category: Optional[str] = Query(None, description="Filter by category"),
    brand: Optional[str] = Query(None, description="Filter by brand"),
    is_visible: Optional[bool] = Query(None, description="Filter by visibility"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    channel: Optional[str] = Query(None, description="Filter by channel"),
    price_min: Optional[float] = Query(None, description="Minimum price"),
    price_max: Optional[float] = Query(None, description="Maximum price"),
    inventory_min: Optional[int] = Query(None, description="Minimum inventory level"),
    inventory_max: Optional[int] = Query(None, description="Maximum inventory level"),
    date_from: Optional[str] = Query(None, description="Start date (ISO format)"),
    date_to: Optional[str] = Query(None, description="End date (ISO format)"),
    sort_by: Optional[str] = Query("created_at", description="Sort field"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc/desc)"),
    db: Session = Depends(get_db)
):
    """Get all products with filtering and pagination."""
    try:
        # Create query parameters
        params = ProductQueryParams(
            page=page,
            limit=limit,
            search=search,
            category=category,
            brand=brand,
            is_visible=is_visible,
            is_featured=is_featured,
            sync_status=SyncStatusEnum(sync_status) if sync_status else None,
            channel=channel,
            price_min=price_min,
            price_max=price_max,
            inventory_min=inventory_min,
            inventory_max=inventory_max,
            date_from=date_from,
            date_to=date_to,
            sort_by=sort_by or "created_at",
            sort_order=sort_order or "desc"
        )
        
        result = ProductService.get_products(db, params)
        
        # Convert to response format
        products = []
        for product in result["products"]:
            products.append({
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "price": product.price,
                "inventory_level": product.inventory_level,
                "is_visible": product.is_visible,
                "is_featured": product.is_featured,
                "sync_status": product.sync_status,
                "created_at": product.created_at,
                "updated_at": product.updated_at,
                "channel": product.channel,
                "category": None,  # TODO: Extract from categories JSON
                "brand": product.brand_name,
                "last_sync_at": product.last_sync_at
            })
        
        return ProductListResponse(
            products=products,
            total=result["total"],
            page=result["page"],
            limit=result["limit"],
            total_pages=result["total_pages"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving products: {str(e)}"
        )


@router.get("/products/{product_id}", response_model=ProductResponse)
async def get_product(product_id: str, db: Session = Depends(get_db)):
    """Get a specific product by ID."""
    try:
        product = ProductService.get_product(db, product_id)
        return product
    except ProductNotFoundException:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving product: {str(e)}"
        )


@router.post("/products", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(product: ProductCreate, db: Session = Depends(get_db)):
    """Create a new product."""
    try:
        return ProductService.create_product(db, product)
    except ProductAlreadyExistsException as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating product: {str(e)}"
        )


@router.put("/products/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str, 
    product: ProductUpdate, 
    db: Session = Depends(get_db)
):
    """Update a product."""
    try:
        return ProductService.update_product(db, product_id, product)
    except ProductNotFoundException:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating product: {str(e)}"
        )


@router.delete("/products/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(product_id: str, db: Session = Depends(get_db)):
    """Delete a product."""
    try:
        ProductService.delete_product(db, product_id)
    except ProductNotFoundException:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting product: {str(e)}"
        )


@router.post("/products/{product_id}/sync", response_model=ProductResponse)
async def sync_product(
    product_id: str, 
    channel: str = Query(..., description="Channel to sync to"),
    db: Session = Depends(get_db)
):
    """Sync a product to external channel."""
    try:
        return ProductService.sync_product(db, product_id, channel)
    except ProductNotFoundException:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing product: {str(e)}"
        ) 