"""
Business logic for the PIM module.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, desc, asc
from datetime import datetime
import uuid

from src.pim.models import Product
from src.pim.schemas import ProductCreate, ProductUpdate, ProductQueryParams, SyncStatusEnum
from src.exceptions import ProductNotFoundException, ProductAlreadyExistsException


class ProductService:
    """Service class for product operations."""
    
    @staticmethod
    def create_product(db: Session, product_data: ProductCreate) -> Product:
        """Create a new product."""
        # Check if product with same SKU already exists
        if product_data.sku:
            existing_product = db.query(Product).filter(Product.sku == product_data.sku).first()
            if existing_product:
                raise ProductAlreadyExistsException(product_data.sku)
        
        # Generate unique ID
        product_id = str(uuid.uuid4())
        
        # Create product instance
        product = Product(
            id=product_id,
            **product_data.dict()
        )
        
        db.add(product)
        db.commit()
        db.refresh(product)
        return product
    
    @staticmethod
    def get_product(db: Session, product_id: str) -> Product:
        """Get a product by ID."""
        product = db.query(Product).filter(Product.id == product_id).first()
        if not product:
            raise ProductNotFoundException(product_id)
        return product
    
    @staticmethod
    def get_products(
        db: Session, 
        params: ProductQueryParams
    ) -> Dict[str, Any]:
        """Get products with filtering and pagination."""
        query = db.query(Product)
        
        # Apply filters
        query = ProductService._apply_filters(query, params)
        
        # Get total count
        total = query.count()
        
        # Apply sorting
        query = ProductService._apply_sorting(query, params.sort_by, params.sort_order)
        
        # Apply pagination
        offset = (params.page - 1) * params.limit
        products = query.offset(offset).limit(params.limit).all()
        
        # Calculate total pages
        total_pages = (total + params.limit - 1) // params.limit
        
        return {
            "products": products,
            "total": total,
            "page": params.page,
            "limit": params.limit,
            "total_pages": total_pages
        }
    
    @staticmethod
    def update_product(db: Session, product_id: str, product_data: ProductUpdate) -> Product:
        """Update a product."""
        product = ProductService.get_product(db, product_id)
        
        # Update fields
        update_data = product_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(product, field, value)
        
        product.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(product)
        return product
    
    @staticmethod
    def delete_product(db: Session, product_id: str) -> bool:
        """Delete a product."""
        product = ProductService.get_product(db, product_id)
        db.delete(product)
        db.commit()
        return True
    
    @staticmethod
    def sync_product(db: Session, product_id: str, channel: str) -> Product:
        """Sync a product to external channel."""
        product = ProductService.get_product(db, product_id)
        
        # Update sync status and timestamp
        product.sync_status = SyncStatusEnum.synced
        product.last_sync_at = datetime.utcnow()
        product.channel = channel
        
        db.commit()
        db.refresh(product)
        return product
    
    @staticmethod
    def _apply_filters(query, params: ProductQueryParams):
        """Apply filters to the query."""
        if params.search:
            search_term = f"%{params.search.lower()}%"
            query = query.filter(
                or_(
                    Product.name.ilike(search_term),
                    Product.sku.ilike(search_term),
                    Product.description.ilike(search_term)
                )
            )
        
        if params.is_visible is not None:
            query = query.filter(Product.is_visible == params.is_visible)
        
        if params.is_featured is not None:
            query = query.filter(Product.is_featured == params.is_featured)
        
        if params.sync_status:
            query = query.filter(Product.sync_status == params.sync_status)
        
        if params.channel:
            query = query.filter(Product.channel == params.channel)
        
        if params.price_min is not None:
            query = query.filter(Product.price >= params.price_min)
        
        if params.price_max is not None:
            query = query.filter(Product.price <= params.price_max)
        
        if params.inventory_min is not None:
            query = query.filter(Product.inventory_level >= params.inventory_min)
        
        if params.inventory_max is not None:
            query = query.filter(Product.inventory_level <= params.inventory_max)
        
        if params.date_from:
            try:
                date_from = datetime.fromisoformat(params.date_from)
                query = query.filter(Product.created_at >= date_from)
            except ValueError:
                pass
        
        if params.date_to:
            try:
                date_to = datetime.fromisoformat(params.date_to)
                query = query.filter(Product.created_at <= date_to)
            except ValueError:
                pass
        
        return query
    
    @staticmethod
    def _apply_sorting(query, sort_by: str, sort_order: str):
        """Apply sorting to the query."""
        sort_column_map = {
            "name": Product.name,
            "price": Product.price,
            "created_at": Product.created_at,
            "updated_at": Product.updated_at,
            "inventory_level": Product.inventory_level,
            "sync_status": Product.sync_status
        }
        
        sort_column = sort_column_map.get(sort_by, Product.created_at)
        
        if sort_order == "asc":
            return query.order_by(asc(sort_column))
        else:
            return query.order_by(desc(sort_column)) 