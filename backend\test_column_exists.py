#!/usr/bin/env python3
"""
Test script to verify database schema and create a simple product
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_simple_product_creation():
    """Test creating a minimal product"""
    print("🧪 Testing minimal product creation...")
    
    # Create a very simple product with only required fields
    product_data = {
        "name": "Simple Test Product",
        "price": 19.99,
        "sku": f"SIMPLE-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/products", json=product_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Product created successfully!")
            print(f"   ID: {data.get('id')}")
            print(f"   Name: {data.get('name')}")
            return data.get('id')
        else:
            print(f"❌ Product creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Database Schema Test...")
    print("=" * 50)
    
    # Test health first
    if not test_health():
        print("\n❌ API is not healthy. Please check if the server is running.")
        exit(1)
    
    # Test simple product creation
    product_id = test_simple_product_creation()
    
    if product_id:
        print(f"\n✅ Test completed successfully! Product ID: {product_id}")
    else:
        print(f"\n❌ Test failed. Check server logs for more details.")
