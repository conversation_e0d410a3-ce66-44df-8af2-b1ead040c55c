#!/usr/bin/env python3
"""
Test the create_product function directly
"""

import sys
import traceback
import asyncio
from datetime import datetime

try:
    from models.product import ProductCreate
    from api.routes.products import create_product
    from core.db import get_db
    
    print("✅ Imports successful")
    
    async def test_create_product_direct():
        """Test create_product function directly"""
        print("\n🧪 Testing create_product function directly...")
        
        try:
            # Create product data
            product_data = ProductCreate(
                name="Direct Test Product",
                price=19.99,
                sku=f"DIRECT-TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            )
            print(f"✅ ProductCreate model created: {product_data.name}")
            
            # Get database session
            db = next(get_db())
            print("✅ Database session created")
            
            # Call create_product function
            result = await create_product(product_data, db)
            print(f"✅ Product created successfully!")
            print(f"   ID: {result.id}")
            print(f"   Name: {result.name}")
            print(f"   Price: {result.price}")
            
            db.close()
            return True
            
        except Exception as e:
            print(f"❌ create_product failed: {e}")
            traceback.print_exc()
            if 'db' in locals():
                db.close()
            return False
    
    # Run the async test
    if __name__ == "__main__":
        print("🚀 Starting Direct Function Test...")
        print("=" * 50)
        
        success = asyncio.run(test_create_product_direct())
        
        if success:
            print(f"\n✅ Direct test completed successfully!")
        else:
            print(f"\n❌ Direct test failed.")
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    traceback.print_exc()
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    traceback.print_exc()
