#!/usr/bin/env python3
"""
Test the Product model directly to identify issues
"""

import sys
import traceback
from datetime import datetime

try:
    from models.product import ProductCreate, Product
    from core.db import get_db, engine
    from sqlalchemy.orm import sessionmaker
    
    print("✅ Imports successful")
    
    # Test Pydantic model creation
    print("\n🧪 Testing Pydantic model...")
    try:
        product_data = ProductCreate(
            name="Test Product",
            price=9.99
        )
        print(f"✅ ProductCreate model created: {product_data.name}")
        print(f"   Model dump: {product_data.model_dump()}")
    except Exception as e:
        print(f"❌ ProductCreate failed: {e}")
        traceback.print_exc()
        sys.exit(1)
    
    # Test SQLAlchemy model creation
    print("\n🗄️ Testing SQLAlchemy model...")
    try:
        # Create a session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Create product instance
        db_product = Product(
            id="test_123",
            **product_data.model_dump()
        )
        print(f"✅ SQLAlchemy Product model created: {db_product.name}")
        
        # Test adding to database (but don't commit)
        db.add(db_product)
        db.flush()  # This will check for SQL errors without committing
        print("✅ Database add/flush successful")
        
        # Rollback to avoid actually saving
        db.rollback()
        db.close()
        
    except Exception as e:
        print(f"❌ SQLAlchemy model failed: {e}")
        traceback.print_exc()
        if 'db' in locals():
            db.rollback()
            db.close()
        sys.exit(1)
    
    print("\n✅ All model tests passed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    traceback.print_exc()
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    traceback.print_exc()
